# 🌟 Krabulon Live Interface

**Advanced Angular Interface for HVAC CRM and Python Mixer with Real-time Transcription Monitoring**

## 🎯 Overview

Krabulon Live Interface is a cutting-edge Angular 17 application that provides a comprehensive, real-time interface for HVAC CRM management and Python Mixer integration. It features advanced transcription monitoring, cosmic-level UI design, and seamless integration with backend services.

## ✨ Features

### 🎤 Real-time Transcription Monitoring
- Live audio transcription processing
- Real-time accuracy metrics
- HVAC keyword detection
- Processing time analytics
- Audio quality assessment

### 👥 Advanced CRM Management
- Customer lifecycle management
- Equipment registry with AI health scoring
- Service ticket system
- Quote generation
- Financial dashboard

### 🐍 Python Mixer Integration
- Direct interface to Python Mixer services
- Email processing pipeline
- Document analysis
- Calendar management
- AI-powered insights

### 🎨 Cosmic Design System
- Material 3 Expressive design
- Dark/Light theme support
- Responsive mobile-first design
- 3D animations with Three.js
- GSAP-powered transitions

### 📊 Advanced Analytics
- Real-time system health monitoring
- Performance metrics dashboard
- Interactive charts with Chart.js
- WebSocket-based live updates
- Comprehensive reporting

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm 9+
- Angular CLI 17+

### Installation

1. **Clone and setup:**
```bash
cd krabulon-angular-interface
chmod +x setup.sh
./setup.sh
```

2. **Start development server:**
```bash
npm start
```

3. **Open in browser:**
```
http://localhost:4200
```

### Manual Installation

```bash
# Install dependencies
npm install

# Start development server
ng serve --host 0.0.0.0 --port 4200

# Build for production
ng build --configuration production
```

## 🏗️ Architecture

### Project Structure
```
src/
├── app/
│   ├── core/                 # Core services and utilities
│   │   ├── services/         # WebSocket, Theme, Auth services
│   │   ├── guards/           # Route guards
│   │   ├── interceptors/     # HTTP interceptors
│   │   └── models/           # TypeScript interfaces
│   ├── shared/               # Shared components and utilities
│   │   ├── components/       # Reusable UI components
│   │   ├── directives/       # Custom directives
│   │   ├── pipes/            # Custom pipes
│   │   └── utils/            # Utility functions
│   ├── features/             # Feature modules
│   │   ├── dashboard/        # Main dashboard
│   │   ├── transcription/    # Transcription monitoring
│   │   ├── crm/              # CRM management
│   │   ├── equipment/        # Equipment registry
│   │   ├── python-mixer/     # Python Mixer interface
│   │   ├── analytics/        # Analytics dashboard
│   │   └── settings/         # Application settings
│   └── layouts/              # Layout components
├── assets/                   # Static assets
├── environments/             # Environment configurations
└── styles.scss              # Global styles
```

### Key Technologies
- **Angular 17** - Latest Angular with standalone components
- **Angular Material** - UI component library
- **PrimeNG** - Additional UI components
- **Chart.js** - Interactive charts and graphs
- **Three.js** - 3D graphics and animations
- **GSAP** - Advanced animations
- **RxJS** - Reactive programming
- **WebSocket** - Real-time communication

## 🔧 Configuration

### Environment Variables

**Development (`src/environments/environment.ts`):**
```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080/api',
  wsUrl: 'ws://localhost:8080/ws',
  pythonMixerUrl: 'http://localhost:8000',
  transcriptionUrl: 'http://localhost:8765',
  enableDebug: true
};
```

**Production (`src/environments/environment.prod.ts`):**
```typescript
export const environment = {
  production: true,
  apiUrl: 'https://api.krabulon.com',
  wsUrl: 'wss://api.krabulon.com/ws',
  pythonMixerUrl: 'https://mixer.krabulon.com',
  transcriptionUrl: 'https://transcription.krabulon.com',
  enableDebug: false
};
```

## 🎨 Theming

### Cosmic Design System
The interface uses a custom "Cosmic" design system with:
- **Primary**: #6366f1 (Indigo)
- **Secondary**: #8b5cf6 (Purple)
- **Accent**: #06b6d4 (Cyan)
- **Success**: #10b981 (Emerald)
- **Warning**: #f59e0b (Amber)
- **Error**: #ef4444 (Red)

### Theme Switching
```typescript
// Inject ThemeService
constructor(private themeService: ThemeService) {}

// Toggle between light/dark
this.themeService.toggleTheme();

// Set specific theme
this.themeService.setThemeByName('cosmic-dark');
```

## 🔌 WebSocket Integration

### Real-time Updates
```typescript
// Connect to WebSocket
this.webSocketService.connect('ws://localhost:8080/ws');

// Subscribe to transcription updates
this.webSocketService.subscribeToTranscriptionUpdates()
  .subscribe(update => {
    console.log('New transcription:', update);
  });

// Send messages
this.webSocketService.sendMessage('start_transcription', { audioData });
```

## 📊 Dashboard Features

### Real-time Metrics
- **Transcription Count**: Active transcription processes
- **Email Processing**: Processed <NAME_EMAIL>
- **System Health**: Overall system status percentage
- **Processing Time**: Average transcription processing time

### Interactive Charts
- **Transcription Activity**: Line chart showing real-time activity
- **System Health**: Doughnut chart with health distribution
- **Performance**: Bar chart showing processing times

### Recent Activities
- Live feed of system events
- Color-coded status indicators
- Timestamp formatting
- Activity type icons

## 🎤 Transcription Monitoring

### Features
- Real-time audio processing status
- HVAC keyword detection
- Accuracy metrics
- Processing time tracking
- Audio quality assessment
- M4A file support

### Integration
```typescript
// Start transcription
this.webSocketService.startTranscription(audioData);

// Monitor progress
this.webSocketService.subscribeToTranscriptionUpdates()
  .subscribe(progress => {
    this.updateProgress(progress);
  });
```

## 👥 CRM Integration

### Customer Management
- Comprehensive customer profiles
- Equipment associations
- Service history
- Communication tracking

### Equipment Registry
- HVAC equipment database
- AI health scoring
- Maintenance scheduling
- Parts management

## 🐍 Python Mixer Interface

### Direct Integration
- Real-time communication with Python Mixer
- Email processing pipeline
- Document analysis
- Calendar management
- AI-powered insights

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1200px
- **Desktop**: > 1200px

### Features
- Mobile-first approach
- Touch-friendly interactions
- Adaptive layouts
- Progressive enhancement

## 🔒 Security

### Authentication
- JWT-based authentication
- Route guards
- Role-based access control
- Session management

### Data Protection
- HTTPS enforcement
- XSS protection
- CSRF tokens
- Input validation

## 🚀 Deployment

### Development
```bash
npm start
# Serves on http://localhost:4200
```

### Production Build
```bash
npm run build:prod
# Outputs to dist/ directory
```

### Docker Deployment
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
EXPOSE 4200
CMD ["npm", "start"]
```

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### E2E Tests
```bash
npm run e2e
```

### Linting
```bash
npm run lint
```

## 📈 Performance

### Optimization Features
- Lazy loading modules
- OnPush change detection
- Tree shaking
- Code splitting
- Service workers (PWA ready)

### Bundle Analysis
```bash
npm run build:prod -- --stats-json
npx webpack-bundle-analyzer dist/stats.json
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- 📧 Email: <EMAIL>
- 💬 Discord: [Krabulon Community](https://discord.gg/krabulon)
- 📖 Documentation: [docs.krabulon.com](https://docs.krabulon.com)

---

**Built with ❤️ by the Krabulon Team**
