{"name": "krabulon-angular-interface", "version": "1.0.0", "description": "Advanced Angular Interface for HVAC CRM and Python Mixer - Krabulon Live Interface", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:prod": "ng serve --configuration production", "build:prod": "ng build --configuration production", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/material": "^17.0.0", "@angular/flex-layout": "^15.0.0-beta.42", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "chart.js": "^4.4.0", "ng2-charts": "^5.0.0", "ngx-socket-io": "^4.7.0", "ngx-toastr": "^18.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "three": "^0.158.0", "@types/three": "^0.158.0", "gsap": "^3.12.2", "lottie-web": "^5.12.2", "ngx-lottie": "^10.0.0", "primeng": "^17.0.0", "primeicons": "^6.0.1", "primeflex": "^3.3.1", "ngx-infinite-scroll": "^17.0.0", "ngx-drag-drop": "^17.0.0", "ngx-color-picker": "^15.0.0", "ngx-file-drop": "^15.0.0", "ngx-pagination": "^6.0.3", "ngx-perfect-scrollbar": "^10.1.1", "ngx-quill": "^24.0.0", "quill": "^1.3.7"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "@types/lodash": "^4.14.202", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-headless": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}