<!-- Dashboard Header -->
<div class="dashboard-header">
  <div class="header-content">
    <div class="header-title">
      <h1>
        <mat-icon class="header-icon">dashboard</mat-icon>
        Krabulon Live Dashboard
      </h1>
      <p class="header-subtitle">Real-time HVAC CRM monitoring and transcription analytics</p>
    </div>
    
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="refreshDashboard()" [disabled]="isLoading">
        <mat-icon>refresh</mat-icon>
        Refresh
      </button>
      
      <button mat-stroked-button color="accent" routerLink="/transcription">
        <mat-icon>mic</mat-icon>
        Start Transcription
      </button>
    </div>
  </div>
  
  <!-- Connection Status Banner -->
  <div class="connection-status" *ngIf="connectionStatus$ | async as status">
    <div class="status-indicator" [class]="status.connected ? 'connected' : 'disconnected'">
      <mat-icon>{{ status.connected ? 'wifi' : 'wifi_off' }}</mat-icon>
      <span>{{ status.connected ? 'Connected' : 'Disconnected' }}</span>
      <span *ngIf="status.reconnecting" class="reconnecting">Reconnecting...</span>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div class="dashboard-loading" *ngIf="isLoading">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading dashboard data...</p>
</div>

<!-- Dashboard Content -->
<div class="dashboard-content" *ngIf="!isLoading">
  
  <!-- Quick Stats Grid -->
  <div class="quick-stats-grid">
    <mat-card class="stat-card dashboard-card" *ngFor="let stat of quickStats; trackBy: trackByStat">
      <mat-card-content>
        <div class="stat-header">
          <mat-icon [style.color]="stat.color">{{ stat.icon }}</mat-icon>
          <div class="stat-trend" [class.trend-up]="stat.trendUp" [class.trend-down]="!stat.trendUp">
            <mat-icon>{{ stat.trendUp ? 'trending_up' : 'trending_down' }}</mat-icon>
            <span>{{ stat.trend }}</span>
          </div>
        </div>
        
        <div class="stat-content">
          <h2 class="stat-value">{{ stat.value | number }}</h2>
          <p class="stat-title">{{ stat.title }}</p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Main Dashboard Grid -->
  <div class="dashboard-grid">
    
    <!-- Transcription Activity Chart -->
    <mat-card class="chart-card dashboard-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>mic</mat-icon>
          Transcription Activity
        </mat-card-title>
        <mat-card-subtitle>Real-time transcription processing</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="chart-container">
          <canvas #transcriptionChart></canvas>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- System Health Chart -->
    <mat-card class="chart-card dashboard-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>health_and_safety</mat-icon>
          System Health
        </mat-card-title>
        <mat-card-subtitle>{{ metrics.systemHealth }}% healthy</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="chart-container">
          <canvas #systemHealthChart></canvas>
        </div>
        <div class="health-metrics">
          <div class="metric">
            <span class="metric-label">Uptime</span>
            <span class="metric-value">{{ metrics.uptime | number:'1.1-1' }}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">Accuracy</span>
            <span class="metric-value">{{ metrics.accuracy | number:'1.1-1' }}%</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Performance Chart -->
    <mat-card class="chart-card dashboard-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>speed</mat-icon>
          Processing Performance
        </mat-card-title>
        <mat-card-subtitle>Average processing time</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="chart-container">
          <canvas #performanceChart></canvas>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Recent Activities -->
    <mat-card class="activities-card dashboard-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>history</mat-icon>
          Recent Activities
        </mat-card-title>
        <mat-card-subtitle>Latest system events</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="activities-list">
          <div class="activity-item" *ngFor="let activity of recentActivities; trackBy: trackByActivity">
            <div class="activity-icon" [style.color]="getActivityColor(activity)">
              <mat-icon>{{ getActivityIcon(activity) }}</mat-icon>
            </div>
            
            <div class="activity-content">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <p class="activity-description">{{ activity.description }}</p>
              <span class="activity-timestamp">{{ formatTimestamp(activity.timestamp) }}</span>
            </div>
            
            <div class="activity-status">
              <mat-icon [style.color]="getActivityColor(activity)">
                {{ activity.status === 'success' ? 'check_circle' : 
                   activity.status === 'warning' ? 'warning' : 
                   activity.status === 'error' ? 'error' : 'info' }}
              </mat-icon>
            </div>
          </div>
          
          <div class="no-activities" *ngIf="recentActivities.length === 0">
            <mat-icon>inbox</mat-icon>
            <p>No recent activities</p>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/activities">
          View All Activities
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- System Status Overview -->
    <mat-card class="status-card dashboard-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>settings</mat-icon>
          System Status
        </mat-card-title>
        <mat-card-subtitle>Service health monitoring</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-indicator transcription" [class.active]="metrics.transcriptionCount > 0">
              <mat-icon>mic</mat-icon>
            </div>
            <div class="status-info">
              <h4>Transcription</h4>
              <p>{{ metrics.transcriptionCount }} processed</p>
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-indicator email" [class.active]="metrics.emailsProcessed > 0">
              <mat-icon>email</mat-icon>
            </div>
            <div class="status-info">
              <h4>Email Processing</h4>
              <p>{{ metrics.emailsProcessed }} emails</p>
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-indicator crm" [class.active]="metrics.customersActive > 0">
              <mat-icon>people</mat-icon>
            </div>
            <div class="status-info">
              <h4>CRM System</h4>
              <p>{{ metrics.customersActive }} active</p>
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-indicator equipment" [class.active]="metrics.equipmentRegistered > 0">
              <mat-icon>build</mat-icon>
            </div>
            <div class="status-info">
              <h4>Equipment</h4>
              <p>{{ metrics.equipmentRegistered }} registered</p>
            </div>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/settings">
          System Settings
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Quick Actions -->
    <mat-card class="actions-card dashboard-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>flash_on</mat-icon>
          Quick Actions
        </mat-card-title>
        <mat-card-subtitle>Common tasks</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="actions-grid">
          <button mat-raised-button color="primary" routerLink="/transcription" class="action-button">
            <mat-icon>mic</mat-icon>
            <span>Start Recording</span>
          </button>
          
          <button mat-raised-button color="accent" routerLink="/crm/customers/new" class="action-button">
            <mat-icon>person_add</mat-icon>
            <span>Add Customer</span>
          </button>
          
          <button mat-raised-button color="warn" routerLink="/equipment/new" class="action-button">
            <mat-icon>build</mat-icon>
            <span>Register Equipment</span>
          </button>
          
          <button mat-stroked-button routerLink="/python-mixer" class="action-button">
            <mat-icon>code</mat-icon>
            <span>Python Mixer</span>
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
