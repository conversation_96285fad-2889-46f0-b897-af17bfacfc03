// Dashboard Header
.dashboard-header {
  margin-bottom: 2rem;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    
    .header-title {
      h1 {
        display: flex;
        align-items: center;
        margin: 0 0 0.5rem;
        font-size: 2rem;
        font-weight: 600;
        color: var(--cosmic-text-primary);
        
        .header-icon {
          margin-right: 0.75rem;
          font-size: 2.5rem;
          color: var(--cosmic-primary);
        }
      }
      
      .header-subtitle {
        margin: 0;
        color: var(--cosmic-text-secondary);
        font-size: 1.1rem;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 1rem;
      
      button {
        min-width: 140px;
        
        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }
  
  .connection-status {
    padding: 0.75rem 1rem;
    border-radius: var(--cosmic-radius);
    background: var(--cosmic-surface);
    border: 1px solid var(--cosmic-border);
    
    .status-indicator {
      display: flex;
      align-items: center;
      font-weight: 500;
      
      mat-icon {
        margin-right: 0.5rem;
      }
      
      &.connected {
        color: var(--cosmic-success);
      }
      
      &.disconnected {
        color: var(--cosmic-error);
      }
      
      .reconnecting {
        margin-left: 0.5rem;
        color: var(--cosmic-warning);
        font-size: 0.9rem;
      }
    }
  }
}

// Loading State
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
  
  mat-spinner {
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--cosmic-text-secondary);
    font-size: 1.1rem;
  }
}

// Quick Stats Grid
.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  
  .stat-card {
    background: var(--cosmic-surface);
    border: 1px solid var(--cosmic-border);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--cosmic-shadow);
    }
    
    mat-card-content {
      padding: 1.5rem;
      
      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        
        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          font-size: 0.9rem;
          font-weight: 500;
          
          mat-icon {
            font-size: 1.2rem;
            width: 1.2rem;
            height: 1.2rem;
            margin-right: 0.25rem;
          }
          
          &.trend-up {
            color: var(--cosmic-success);
          }
          
          &.trend-down {
            color: var(--cosmic-error);
          }
        }
      }
      
      .stat-content {
        .stat-value {
          font-size: 2.5rem;
          font-weight: 700;
          margin: 0 0 0.25rem;
          color: var(--cosmic-text-primary);
        }
        
        .stat-title {
          margin: 0;
          color: var(--cosmic-text-secondary);
          font-size: 0.95rem;
        }
      }
    }
  }
}

// Dashboard Grid
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1.5rem;
  
  .dashboard-card {
    background: var(--cosmic-surface);
    border: 1px solid var(--cosmic-border);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: var(--cosmic-shadow);
    }
  }
  
  // Chart Cards
  .chart-card {
    grid-column: span 6;
    
    mat-card-header {
      mat-card-title {
        display: flex;
        align-items: center;
        
        mat-icon {
          margin-right: 0.5rem;
          color: var(--cosmic-primary);
        }
      }
    }
    
    .chart-container {
      height: 300px;
      position: relative;
    }
    
    .health-metrics {
      display: flex;
      justify-content: space-around;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid var(--cosmic-border);
      
      .metric {
        text-align: center;
        
        .metric-label {
          display: block;
          font-size: 0.9rem;
          color: var(--cosmic-text-secondary);
          margin-bottom: 0.25rem;
        }
        
        .metric-value {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--cosmic-primary);
        }
      }
    }
  }
  
  // Activities Card
  .activities-card {
    grid-column: span 6;
    
    .activities-list {
      max-height: 400px;
      overflow-y: auto;
      
      .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid var(--cosmic-border);
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          margin-right: 1rem;
          
          mat-icon {
            font-size: 1.5rem;
            width: 1.5rem;
            height: 1.5rem;
          }
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            margin: 0 0 0.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--cosmic-text-primary);
          }
          
          .activity-description {
            margin: 0 0 0.5rem;
            font-size: 0.9rem;
            color: var(--cosmic-text-secondary);
          }
          
          .activity-timestamp {
            font-size: 0.8rem;
            color: var(--cosmic-text-secondary);
          }
        }
        
        .activity-status {
          mat-icon {
            font-size: 1.2rem;
            width: 1.2rem;
            height: 1.2rem;
          }
        }
      }
      
      .no-activities {
        text-align: center;
        padding: 2rem;
        color: var(--cosmic-text-secondary);
        
        mat-icon {
          font-size: 3rem;
          width: 3rem;
          height: 3rem;
          margin-bottom: 1rem;
          opacity: 0.5;
        }
      }
    }
  }
  
  // Status Card
  .status-card {
    grid-column: span 4;
    
    .status-grid {
      display: grid;
      gap: 1rem;
      
      .status-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: var(--cosmic-radius);
        background: rgba(99, 102, 241, 0.05);
        border: 1px solid rgba(99, 102, 241, 0.1);
        
        .status-indicator {
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
          background: rgba(99, 102, 241, 0.1);
          color: var(--cosmic-text-secondary);
          transition: all 0.3s ease;
          
          &.active {
            background: var(--cosmic-primary);
            color: white;
            box-shadow: 0 0 15px rgba(99, 102, 241, 0.3);
          }
          
          mat-icon {
            font-size: 1.5rem;
            width: 1.5rem;
            height: 1.5rem;
          }
        }
        
        .status-info {
          h4 {
            margin: 0 0 0.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--cosmic-text-primary);
          }
          
          p {
            margin: 0;
            font-size: 0.9rem;
            color: var(--cosmic-text-secondary);
          }
        }
      }
    }
  }
  
  // Actions Card
  .actions-card {
    grid-column: span 8;
    
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      
      .action-button {
        height: 4rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        
        mat-icon {
          margin-bottom: 0.5rem;
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
        
        span {
          font-size: 0.9rem;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .dashboard-grid {
    .chart-card,
    .activities-card {
      grid-column: span 12;
    }
    
    .status-card {
      grid-column: span 6;
    }
    
    .actions-card {
      grid-column: span 6;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    .header-content {
      flex-direction: column;
      gap: 1rem;
      
      .header-actions {
        width: 100%;
        justify-content: stretch;
        
        button {
          flex: 1;
        }
      }
    }
  }
  
  .quick-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    
    .dashboard-card {
      grid-column: span 1;
    }
  }
}

// Dark Theme Support
.dark-theme {
  .dashboard-card {
    background: var(--cosmic-dark-surface);
    border-color: var(--cosmic-dark-border);
  }
  
  .connection-status {
    background: var(--cosmic-dark-surface);
    border-color: var(--cosmic-dark-border);
  }
  
  .status-item {
    background: rgba(99, 102, 241, 0.1) !important;
    border-color: rgba(99, 102, 241, 0.2) !important;
  }
}
