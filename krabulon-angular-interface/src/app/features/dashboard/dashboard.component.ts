import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, <PERSON>Child, ElementRef } from '@angular/core';
import { Subject, takeUntil, interval, combineLatest } from 'rxjs';
import { WebSocketService } from '@core/services/websocket.service';
import { ThemeService } from '@core/services/theme.service';
import { NotificationService } from '@core/services/notification.service';
import { Chart, ChartConfiguration, ChartType } from 'chart.js';
import { gsap } from 'gsap';

export interface DashboardMetrics {
  transcriptionCount: number;
  emailsProcessed: number;
  customersActive: number;
  equipmentRegistered: number;
  systemHealth: number;
  processingTime: number;
  accuracy: number;
  uptime: number;
}

export interface RecentActivity {
  id: string;
  type: 'transcription' | 'email' | 'crm' | 'equipment';
  title: string;
  description: string;
  timestamp: Date;
  status: 'success' | 'warning' | 'error' | 'info';
  icon: string;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('transcriptionChart', { static: false }) transcriptionChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('systemHealthChart', { static: false }) systemHealthChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('performanceChart', { static: false }) performanceChartRef!: ElementRef<HTMLCanvasElement>;

  private destroy$ = new Subject<void>();
  private transcriptionChart: Chart | null = null;
  private systemHealthChart: Chart | null = null;
  private performanceChart: Chart | null = null;

  // Dashboard data
  metrics: DashboardMetrics = {
    transcriptionCount: 0,
    emailsProcessed: 0,
    customersActive: 0,
    equipmentRegistered: 0,
    systemHealth: 0,
    processingTime: 0,
    accuracy: 0,
    uptime: 0
  };

  recentActivities: RecentActivity[] = [];
  isLoading = true;
  connectionStatus$ = this.webSocketService.connectionStatus$;
  currentTheme$ = this.themeService.currentTheme$;

  // Real-time data
  transcriptionData: number[] = [];
  systemHealthData: number[] = [];
  performanceData: number[] = [];
  timeLabels: string[] = [];

  // Quick stats
  quickStats = [
    {
      title: 'Active Transcriptions',
      value: 0,
      icon: 'mic',
      color: 'var(--cosmic-primary)',
      trend: '+12%',
      trendUp: true
    },
    {
      title: 'Emails Processed',
      value: 0,
      icon: 'email',
      color: 'var(--cosmic-secondary)',
      trend: '+8%',
      trendUp: true
    },
    {
      title: 'System Health',
      value: 0,
      icon: 'health_and_safety',
      color: 'var(--cosmic-success)',
      trend: '+2%',
      trendUp: true
    },
    {
      title: 'Processing Time',
      value: 0,
      icon: 'speed',
      color: 'var(--cosmic-accent)',
      trend: '-5%',
      trendUp: false
    }
  ];

  constructor(
    private webSocketService: WebSocketService,
    private themeService: ThemeService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.initializeDashboard();
    this.setupWebSocketListeners();
    this.startRealTimeUpdates();
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.destroyCharts();
  }

  private initializeDashboard(): void {
    // Animate dashboard entrance
    gsap.from('.dashboard-card', {
      duration: 0.8,
      y: 50,
      opacity: 0,
      stagger: 0.1,
      ease: 'power2.out'
    });

    // Initialize time labels
    this.initializeTimeLabels();
  }

  private setupWebSocketListeners(): void {
    // Listen for transcription updates
    this.webSocketService.subscribeToTranscriptionUpdates()
      .pipe(takeUntil(this.destroy$))
      .subscribe((update) => {
        this.handleTranscriptionUpdate(update);
      });

    // Listen for system status updates
    this.webSocketService.subscribeToSystemStatus()
      .pipe(takeUntil(this.destroy$))
      .subscribe((status) => {
        this.handleSystemStatusUpdate(status);
      });

    // Listen for email updates
    this.webSocketService.subscribeToEmailUpdates()
      .pipe(takeUntil(this.destroy$))
      .subscribe((update) => {
        this.handleEmailUpdate(update);
      });

    // Listen for CRM updates
    this.webSocketService.subscribeToCRMUpdates()
      .pipe(takeUntil(this.destroy$))
      .subscribe((update) => {
        this.handleCRMUpdate(update);
      });
  }

  private startRealTimeUpdates(): void {
    // Update charts every 5 seconds
    interval(5000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateRealTimeData();
        this.updateCharts();
      });

    // Request initial system status
    this.webSocketService.requestSystemStatus();
  }

  private loadInitialData(): void {
    // Simulate loading initial data
    setTimeout(() => {
      this.metrics = {
        transcriptionCount: 156,
        emailsProcessed: 2341,
        customersActive: 89,
        equipmentRegistered: 234,
        systemHealth: 94,
        processingTime: 12.5,
        accuracy: 96.8,
        uptime: 99.2
      };

      this.updateQuickStats();
      this.loadRecentActivities();
      this.initializeCharts();
      this.isLoading = false;
    }, 1500);
  }

  private updateQuickStats(): void {
    this.quickStats[0].value = this.metrics.transcriptionCount;
    this.quickStats[1].value = this.metrics.emailsProcessed;
    this.quickStats[2].value = this.metrics.systemHealth;
    this.quickStats[3].value = this.metrics.processingTime;
  }

  private loadRecentActivities(): void {
    this.recentActivities = [
      {
        id: '1',
        type: 'transcription',
        title: 'Audio Transcription Completed',
        description: 'Customer service call transcribed successfully',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        status: 'success',
        icon: 'mic'
      },
      {
        id: '2',
        type: 'email',
        title: 'Email Processed',
        description: 'New <NAME_EMAIL> processed',
        timestamp: new Date(Date.now() - 12 * 60 * 1000),
        status: 'info',
        icon: 'email'
      },
      {
        id: '3',
        type: 'crm',
        title: 'Customer Updated',
        description: 'Customer profile updated with new equipment',
        timestamp: new Date(Date.now() - 18 * 60 * 1000),
        status: 'success',
        icon: 'person'
      },
      {
        id: '4',
        type: 'equipment',
        title: 'Equipment Registered',
        description: 'New LG Split AC unit added to registry',
        timestamp: new Date(Date.now() - 25 * 60 * 1000),
        status: 'success',
        icon: 'build'
      },
      {
        id: '5',
        type: 'transcription',
        title: 'Processing Warning',
        description: 'Audio quality below threshold',
        timestamp: new Date(Date.now() - 35 * 60 * 1000),
        status: 'warning',
        icon: 'warning'
      }
    ];
  }

  private initializeTimeLabels(): void {
    const now = new Date();
    for (let i = 19; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60000); // 1 minute intervals
      this.timeLabels.push(time.toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
    }
  }

  private updateRealTimeData(): void {
    // Simulate real-time data updates
    this.transcriptionData.push(Math.floor(Math.random() * 10) + 5);
    this.systemHealthData.push(Math.floor(Math.random() * 10) + 85);
    this.performanceData.push(Math.random() * 5 + 10);

    // Keep only last 20 data points
    if (this.transcriptionData.length > 20) {
      this.transcriptionData.shift();
      this.systemHealthData.shift();
      this.performanceData.shift();
    }

    // Update time labels
    const now = new Date();
    this.timeLabels.push(now.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    }));
    
    if (this.timeLabels.length > 20) {
      this.timeLabels.shift();
    }
  }

  private handleTranscriptionUpdate(update: any): void {
    this.metrics.transcriptionCount++;
    this.addRecentActivity({
      id: Date.now().toString(),
      type: 'transcription',
      title: 'New Transcription',
      description: `Processed: ${update.filename || 'Audio file'}`,
      timestamp: new Date(),
      status: update.success ? 'success' : 'error',
      icon: 'mic'
    });
  }

  private handleSystemStatusUpdate(status: any): void {
    this.metrics.systemHealth = status.health || this.metrics.systemHealth;
    this.metrics.uptime = status.uptime || this.metrics.uptime;
  }

  private handleEmailUpdate(update: any): void {
    this.metrics.emailsProcessed++;
    this.addRecentActivity({
      id: Date.now().toString(),
      type: 'email',
      title: 'Email Processed',
      description: `From: ${update.sender || 'Unknown'}`,
      timestamp: new Date(),
      status: 'info',
      icon: 'email'
    });
  }

  private handleCRMUpdate(update: any): void {
    this.addRecentActivity({
      id: Date.now().toString(),
      type: 'crm',
      title: 'CRM Update',
      description: update.description || 'Record updated',
      timestamp: new Date(),
      status: 'success',
      icon: 'person'
    });
  }

  private addRecentActivity(activity: RecentActivity): void {
    this.recentActivities.unshift(activity);
    if (this.recentActivities.length > 10) {
      this.recentActivities.pop();
    }
  }

  private initializeCharts(): void {
    setTimeout(() => {
      this.createTranscriptionChart();
      this.createSystemHealthChart();
      this.createPerformanceChart();
    }, 100);
  }

  private createTranscriptionChart(): void {
    if (!this.transcriptionChartRef?.nativeElement) return;

    const ctx = this.transcriptionChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    this.transcriptionChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: this.timeLabels,
        datasets: [{
          label: 'Transcriptions per minute',
          data: this.transcriptionData,
          borderColor: 'var(--cosmic-primary)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          },
          x: {
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          }
        }
      }
    });
  }

  private createSystemHealthChart(): void {
    if (!this.systemHealthChartRef?.nativeElement) return;

    const ctx = this.systemHealthChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    this.systemHealthChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Healthy', 'Warning', 'Critical'],
        datasets: [{
          data: [this.metrics.systemHealth, 100 - this.metrics.systemHealth, 0],
          backgroundColor: [
            'var(--cosmic-success)',
            'var(--cosmic-warning)',
            'var(--cosmic-error)'
          ],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '70%',
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
  }

  private createPerformanceChart(): void {
    if (!this.performanceChartRef?.nativeElement) return;

    const ctx = this.performanceChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    this.performanceChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: this.timeLabels.slice(-10),
        datasets: [{
          label: 'Processing Time (s)',
          data: this.performanceData.slice(-10),
          backgroundColor: 'var(--cosmic-accent)',
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  private updateCharts(): void {
    if (this.transcriptionChart) {
      this.transcriptionChart.data.labels = this.timeLabels;
      this.transcriptionChart.data.datasets[0].data = this.transcriptionData;
      this.transcriptionChart.update('none');
    }

    if (this.systemHealthChart) {
      this.systemHealthChart.data.datasets[0].data = [
        this.metrics.systemHealth,
        100 - this.metrics.systemHealth,
        0
      ];
      this.systemHealthChart.update('none');
    }

    if (this.performanceChart) {
      this.performanceChart.data.labels = this.timeLabels.slice(-10);
      this.performanceChart.data.datasets[0].data = this.performanceData.slice(-10);
      this.performanceChart.update('none');
    }
  }

  private destroyCharts(): void {
    if (this.transcriptionChart) {
      this.transcriptionChart.destroy();
    }
    if (this.systemHealthChart) {
      this.systemHealthChart.destroy();
    }
    if (this.performanceChart) {
      this.performanceChart.destroy();
    }
  }

  // Public methods for template
  getActivityIcon(activity: RecentActivity): string {
    return activity.icon;
  }

  getActivityColor(activity: RecentActivity): string {
    const colors = {
      success: 'var(--cosmic-success)',
      warning: 'var(--cosmic-warning)',
      error: 'var(--cosmic-error)',
      info: 'var(--cosmic-accent)'
    };
    return colors[activity.status];
  }

  formatTimestamp(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  }

  refreshDashboard(): void {
    this.isLoading = true;
    this.webSocketService.requestSystemStatus();
    this.webSocketService.requestTranscriptionStatus();
    
    setTimeout(() => {
      this.isLoading = false;
      this.notificationService.showSuccess('Dashboard refreshed successfully');
    }, 1000);
  }
}
