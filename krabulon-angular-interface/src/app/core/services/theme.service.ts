import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ThemeConfig {
  name: string;
  displayName: string;
  isDark: boolean;
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  textPrimary: string;
  textSecondary: string;
}

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'krabulon-theme';
  
  private themes: ThemeConfig[] = [
    {
      name: 'cosmic-light',
      displayName: 'Cosmic Light',
      isDark: false,
      primary: '#6366f1',
      secondary: '#8b5cf6',
      accent: '#06b6d4',
      background: '#f8fafc',
      surface: '#ffffff',
      textPrimary: '#1e293b',
      textSecondary: '#64748b'
    },
    {
      name: 'cosmic-dark',
      displayName: 'Cosmic Dark',
      isDark: true,
      primary: '#6366f1',
      secondary: '#8b5cf6',
      accent: '#06b6d4',
      background: '#0f172a',
      surface: '#1e293b',
      textPrimary: '#f1f5f9',
      textSecondary: '#94a3b8'
    },
    {
      name: 'hvac-blue',
      displayName: 'HVAC Blue',
      isDark: false,
      primary: '#0ea5e9',
      secondary: '#0284c7',
      accent: '#06b6d4',
      background: '#f0f9ff',
      surface: '#ffffff',
      textPrimary: '#0c4a6e',
      textSecondary: '#0369a1'
    },
    {
      name: 'professional-dark',
      displayName: 'Professional Dark',
      isDark: true,
      primary: '#3b82f6',
      secondary: '#1d4ed8',
      accent: '#10b981',
      background: '#111827',
      surface: '#1f2937',
      textPrimary: '#f9fafb',
      textSecondary: '#d1d5db'
    }
  ];

  private currentThemeSubject = new BehaviorSubject<ThemeConfig>(this.themes[0]);
  public currentTheme$ = this.currentThemeSubject.asObservable();

  private isDarkModeSubject = new BehaviorSubject<boolean>(false);
  public isDarkMode$ = this.isDarkModeSubject.asObservable();

  constructor() {
    this.initializeTheme();
  }

  initializeTheme(): void {
    // Check for saved theme preference
    const savedTheme = localStorage.getItem(this.THEME_KEY);
    
    if (savedTheme) {
      const theme = this.themes.find(t => t.name === savedTheme);
      if (theme) {
        this.setTheme(theme);
        return;
      }
    }

    // Check system preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const defaultTheme = prefersDark ? this.themes[1] : this.themes[0];
    this.setTheme(defaultTheme);

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)')
      .addEventListener('change', (e) => {
        if (!localStorage.getItem(this.THEME_KEY)) {
          const systemTheme = e.matches ? this.themes[1] : this.themes[0];
          this.setTheme(systemTheme);
        }
      });
  }

  setTheme(theme: ThemeConfig): void {
    this.currentThemeSubject.next(theme);
    this.isDarkModeSubject.next(theme.isDark);
    
    // Apply theme to document
    this.applyThemeToDocument(theme);
    
    // Save preference
    localStorage.setItem(this.THEME_KEY, theme.name);
  }

  toggleTheme(): void {
    const currentTheme = this.currentThemeSubject.value;
    const newTheme = currentTheme.isDark ? this.themes[0] : this.themes[1];
    this.setTheme(newTheme);
  }

  setThemeByName(themeName: string): void {
    const theme = this.themes.find(t => t.name === themeName);
    if (theme) {
      this.setTheme(theme);
    }
  }

  getAvailableThemes(): ThemeConfig[] {
    return [...this.themes];
  }

  getCurrentTheme(): ThemeConfig {
    return this.currentThemeSubject.value;
  }

  isDarkMode(): boolean {
    return this.isDarkModeSubject.value;
  }

  private applyThemeToDocument(theme: ThemeConfig): void {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    root.style.setProperty('--cosmic-primary', theme.primary);
    root.style.setProperty('--cosmic-secondary', theme.secondary);
    root.style.setProperty('--cosmic-accent', theme.accent);
    root.style.setProperty('--cosmic-background', theme.background);
    root.style.setProperty('--cosmic-surface', theme.surface);
    root.style.setProperty('--cosmic-text-primary', theme.textPrimary);
    root.style.setProperty('--cosmic-text-secondary', theme.textSecondary);
    
    // Apply theme class to body
    document.body.className = document.body.className
      .replace(/\b\w+-theme\b/g, '');
    document.body.classList.add(`${theme.name}-theme`);
    
    if (theme.isDark) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }

    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme.primary);
  }

  private updateMetaThemeColor(color: string): void {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.setAttribute('name', 'theme-color');
      document.head.appendChild(metaThemeColor);
    }
    
    metaThemeColor.setAttribute('content', color);
  }

  // Utility methods for theme-aware styling
  getThemeColor(colorName: 'primary' | 'secondary' | 'accent' | 'background' | 'surface' | 'textPrimary' | 'textSecondary'): string {
    const theme = this.getCurrentTheme();
    return theme[colorName];
  }

  generateGradient(color1?: string, color2?: string): string {
    const theme = this.getCurrentTheme();
    const primary = color1 || theme.primary;
    const secondary = color2 || theme.secondary;
    return `linear-gradient(135deg, ${primary}, ${secondary})`;
  }

  generateBoxShadow(intensity: 'light' | 'medium' | 'heavy' = 'medium'): string {
    const theme = this.getCurrentTheme();
    const shadowColor = theme.isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.1)';
    
    switch (intensity) {
      case 'light':
        return `0 2px 4px ${shadowColor}`;
      case 'medium':
        return `0 4px 6px -1px ${shadowColor}`;
      case 'heavy':
        return `0 10px 15px -3px ${shadowColor}`;
      default:
        return `0 4px 6px -1px ${shadowColor}`;
    }
  }
}
