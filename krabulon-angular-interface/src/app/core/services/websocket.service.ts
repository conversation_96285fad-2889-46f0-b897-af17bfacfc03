import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, timer } from 'rxjs';
import { filter, map, takeUntil, retry, delay } from 'rxjs/operators';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  id?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  reconnecting: boolean;
  lastConnected?: Date;
  reconnectAttempts: number;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private messageSubject = new Subject<WebSocketMessage>();
  private connectionStatusSubject = new BehaviorSubject<ConnectionStatus>({
    connected: false,
    reconnecting: false,
    reconnectAttempts: 0
  });
  
  private reconnectInterval = 5000; // 5 seconds
  private maxReconnectAttempts = 10;
  private reconnectTimer: any;
  private url = '';
  private destroy$ = new Subject<void>();

  public messages$ = this.messageSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();

  constructor() {
    // Auto-reconnect on page visibility change
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && !this.isConnected()) {
        this.reconnect();
      }
    });
  }

  connect(url: string): void {
    this.url = url;
    this.createConnection();
  }

  disconnect(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    
    if (this.socket) {
      this.socket.close(1000, 'Manual disconnect');
      this.socket = null;
    }
    
    this.updateConnectionStatus({
      connected: false,
      reconnecting: false,
      reconnectAttempts: 0
    });
  }

  send(message: WebSocketMessage): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }

  sendMessage(type: string, data: any): void {
    const message: WebSocketMessage = {
      type,
      data,
      timestamp: Date.now(),
      id: this.generateMessageId()
    };
    this.send(message);
  }

  onMessage(messageType?: string): Observable<any> {
    return this.messages$.pipe(
      filter(message => !messageType || message.type === messageType),
      map(message => message.data)
    );
  }

  onConnectionStatus(): Observable<ConnectionStatus> {
    return this.connectionStatus$;
  }

  isConnected(): boolean {
    return this.connectionStatusSubject.value.connected;
  }

  reconnect(): void {
    if (this.url) {
      this.createConnection();
    }
  }

  private createConnection(): void {
    if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
      return; // Already connecting
    }

    try {
      this.socket = new WebSocket(this.url);
      this.setupEventListeners();
      
      this.updateConnectionStatus({
        ...this.connectionStatusSubject.value,
        reconnecting: true
      });
      
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.handleConnectionError();
    }
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.onopen = (event) => {
      console.log('WebSocket connected:', event);
      
      this.updateConnectionStatus({
        connected: true,
        reconnecting: false,
        lastConnected: new Date(),
        reconnectAttempts: 0
      });

      // Clear reconnect timer
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      // Send connection acknowledgment
      this.sendMessage('connection_ack', {
        clientId: this.generateClientId(),
        timestamp: Date.now()
      });
    };

    this.socket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.messageSubject.next(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket disconnected:', event);
      
      this.updateConnectionStatus({
        connected: false,
        reconnecting: false,
        reconnectAttempts: this.connectionStatusSubject.value.reconnectAttempts
      });

      // Attempt to reconnect if not manually closed
      if (event.code !== 1000) {
        this.handleConnectionError();
      }
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.handleConnectionError();
    };
  }

  private handleConnectionError(): void {
    const currentStatus = this.connectionStatusSubject.value;
    
    if (currentStatus.reconnectAttempts < this.maxReconnectAttempts) {
      const newAttempts = currentStatus.reconnectAttempts + 1;
      
      this.updateConnectionStatus({
        connected: false,
        reconnecting: true,
        reconnectAttempts: newAttempts
      });

      // Exponential backoff for reconnection
      const delay = Math.min(this.reconnectInterval * Math.pow(2, newAttempts - 1), 30000);
      
      this.reconnectTimer = setTimeout(() => {
        console.log(`Attempting to reconnect (${newAttempts}/${this.maxReconnectAttempts})...`);
        this.createConnection();
      }, delay);
      
    } else {
      console.error('Max reconnection attempts reached');
      this.updateConnectionStatus({
        connected: false,
        reconnecting: false,
        reconnectAttempts: currentStatus.reconnectAttempts
      });
    }
  }

  private updateConnectionStatus(status: Partial<ConnectionStatus>): void {
    const currentStatus = this.connectionStatusSubject.value;
    this.connectionStatusSubject.next({ ...currentStatus, ...status });
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Utility methods for specific message types
  subscribeToTranscriptionUpdates(): Observable<any> {
    return this.onMessage('transcription_update');
  }

  subscribeToSystemStatus(): Observable<any> {
    return this.onMessage('system_status');
  }

  subscribeToEmailUpdates(): Observable<any> {
    return this.onMessage('email_update');
  }

  subscribeToCRMUpdates(): Observable<any> {
    return this.onMessage('crm_update');
  }

  subscribeToEquipmentUpdates(): Observable<any> {
    return this.onMessage('equipment_update');
  }

  // Send specific message types
  requestSystemStatus(): void {
    this.sendMessage('request_system_status', {});
  }

  requestTranscriptionStatus(): void {
    this.sendMessage('request_transcription_status', {});
  }

  startTranscription(audioData: any): void {
    this.sendMessage('start_transcription', { audioData });
  }

  stopTranscription(): void {
    this.sendMessage('stop_transcription', {});
  }

  updateCRMRecord(recordType: string, recordId: string, data: any): void {
    this.sendMessage('update_crm_record', {
      recordType,
      recordId,
      data,
      timestamp: Date.now()
    });
  }

  requestEquipmentData(filters?: any): void {
    this.sendMessage('request_equipment_data', { filters });
  }
}
