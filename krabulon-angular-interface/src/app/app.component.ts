import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Subject, takeUntil, filter } from 'rxjs';
import { ThemeService } from '@core/services/theme.service';
import { WebSocketService } from '@core/services/websocket.service';
import { NotificationService } from '@core/services/notification.service';
import { AuthService } from '@core/services/auth.service';
import { LoadingService } from '@core/services/loading.service';
import * as THREE from 'three';
import { gsap } from 'gsap';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild('cosmicBackground', { static: true }) cosmicBackground!: ElementRef<HTMLCanvasElement>;
  
  title = 'Krabulon Live Interface';
  isLoading$ = this.loadingService.isLoading$;
  isAuthenticated$ = this.authService.isAuthenticated$;
  currentUser$ = this.authService.currentUser$;
  
  private destroy$ = new Subject<void>();
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private renderer!: THREE.WebGLRenderer;
  private particles!: THREE.Points;
  private animationId!: number;

  // Navigation state
  sidenavOpened = true;
  currentRoute = '';
  
  // System status
  systemStatus = {
    transcription: false,
    email: false,
    database: false,
    ai: false
  };

  constructor(
    private router: Router,
    private themeService: ThemeService,
    private webSocketService: WebSocketService,
    private notificationService: NotificationService,
    private authService: AuthService,
    private loadingService: LoadingService
  ) {
    this.initializeRouterEvents();
  }

  ngOnInit(): void {
    this.initializeApplication();
    this.initializeCosmicBackground();
    this.connectWebSocket();
    this.monitorSystemStatus();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.cleanupCosmicBackground();
    this.webSocketService.disconnect();
  }

  private initializeApplication(): void {
    // Initialize theme
    this.themeService.initializeTheme();
    
    // Show welcome notification
    this.notificationService.showSuccess(
      'Welcome to Krabulon Live Interface!',
      'Advanced HVAC CRM with real-time transcription monitoring'
    );
  }

  private initializeRouterEvents(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationEnd) => {
        this.currentRoute = event.url;
        this.updatePageTitle(event.url);
      });
  }

  private updatePageTitle(url: string): void {
    const routeTitles: { [key: string]: string } = {
      '/dashboard': 'Dashboard - Krabulon Live',
      '/transcription': 'Transcription Monitor - Krabulon Live',
      '/crm': 'CRM Management - Krabulon Live',
      '/equipment': 'Equipment Registry - Krabulon Live',
      '/analytics': 'Analytics - Krabulon Live',
      '/settings': 'Settings - Krabulon Live'
    };
    
    document.title = routeTitles[url] || 'Krabulon Live Interface';
  }

  private initializeCosmicBackground(): void {
    if (!this.cosmicBackground?.nativeElement) return;

    const canvas = this.cosmicBackground.nativeElement;
    
    // Scene setup
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ canvas, alpha: true });
    
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setClearColor(0x000000, 0);
    
    // Create cosmic particles
    this.createCosmicParticles();
    
    // Position camera
    this.camera.position.z = 5;
    
    // Start animation
    this.animateCosmicBackground();
    
    // Handle window resize
    window.addEventListener('resize', () => this.onWindowResize());
  }

  private createCosmicParticles(): void {
    const particleCount = 1000;
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      // Random positions
      positions[i3] = (Math.random() - 0.5) * 20;
      positions[i3 + 1] = (Math.random() - 0.5) * 20;
      positions[i3 + 2] = (Math.random() - 0.5) * 20;
      
      // Cosmic colors (blue to purple gradient)
      const hue = Math.random() * 0.3 + 0.6; // Blue to purple range
      const color = new THREE.Color().setHSL(hue, 0.8, 0.6);
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
    }
    
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    
    const material = new THREE.PointsMaterial({
      size: 2,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });
    
    this.particles = new THREE.Points(geometry, material);
    this.scene.add(this.particles);
  }

  private animateCosmicBackground(): void {
    this.animationId = requestAnimationFrame(() => this.animateCosmicBackground());
    
    // Rotate particles
    if (this.particles) {
      this.particles.rotation.x += 0.001;
      this.particles.rotation.y += 0.002;
    }
    
    this.renderer.render(this.scene, this.camera);
  }

  private onWindowResize(): void {
    if (!this.camera || !this.renderer) return;
    
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  private cleanupCosmicBackground(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    
    if (this.renderer) {
      this.renderer.dispose();
    }
  }

  private connectWebSocket(): void {
    this.webSocketService.connect('ws://localhost:8080/ws');
    
    // Listen for system status updates
    this.webSocketService.onMessage('system_status')
      .pipe(takeUntil(this.destroy$))
      .subscribe((status: any) => {
        this.systemStatus = { ...this.systemStatus, ...status };
      });
    
    // Listen for transcription updates
    this.webSocketService.onMessage('transcription_update')
      .pipe(takeUntil(this.destroy$))
      .subscribe((update: any) => {
        this.notificationService.showInfo(
          'New Transcription',
          `Processed: ${update.filename}`
        );
      });
  }

  private monitorSystemStatus(): void {
    // Simulate system status monitoring
    setInterval(() => {
      // This would be replaced with actual health checks
      this.systemStatus = {
        transcription: Math.random() > 0.3,
        email: Math.random() > 0.2,
        database: Math.random() > 0.1,
        ai: Math.random() > 0.4
      };
    }, 5000);
  }

  toggleSidenav(): void {
    this.sidenavOpened = !this.sidenavOpened;
    
    // Animate sidenav toggle
    gsap.to('.sidenav-content', {
      duration: 0.3,
      x: this.sidenavOpened ? 0 : -280,
      ease: 'power2.out'
    });
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  getSystemHealthPercentage(): number {
    const statusValues = Object.values(this.systemStatus);
    const healthyCount = statusValues.filter(status => status).length;
    return Math.round((healthyCount / statusValues.length) * 100);
  }

  getSystemHealthColor(): string {
    const health = this.getSystemHealthPercentage();
    if (health >= 80) return 'var(--cosmic-success)';
    if (health >= 60) return 'var(--cosmic-warning)';
    return 'var(--cosmic-error)';
  }
}
