<!-- Cosmic Background Canvas -->
<canvas #cosmicBackground class="cosmic-background"></canvas>

<!-- Loading Overlay -->
<div class="loading-overlay" *ngIf="isLoading$ | async">
  <div class="loading-content">
    <div class="cosmic-spinner"></div>
    <p class="loading-text">Loading Krabulon Interface...</p>
  </div>
</div>

<!-- Main Application Layout -->
<div class="app-container" *ngIf="!(isLoading$ | async)">
  
  <!-- Top Navigation Bar -->
  <mat-toolbar class="cosmic-toolbar" color="primary">
    <button mat-icon-button (click)="toggleSidenav()" class="menu-button">
      <mat-icon>menu</mat-icon>
    </button>
    
    <span class="app-title">
      <mat-icon class="title-icon">auto_awesome</mat-icon>
      Krabulon Live Interface
    </span>
    
    <span class="spacer"></span>
    
    <!-- System Health Indicator -->
    <div class="system-health" [style.color]="getSystemHealthColor()">
      <mat-icon>health_and_safety</mat-icon>
      <span class="health-percentage">{{ getSystemHealthPercentage() }}%</span>
    </div>
    
    <!-- Real-time Status Indicators -->
    <div class="status-indicators">
      <mat-icon 
        class="status-icon" 
        [class.active]="systemStatus.transcription"
        matTooltip="Transcription Service">
        mic
      </mat-icon>
      <mat-icon 
        class="status-icon" 
        [class.active]="systemStatus.email"
        matTooltip="Email Processing">
        email
      </mat-icon>
      <mat-icon 
        class="status-icon" 
        [class.active]="systemStatus.database"
        matTooltip="Database Connection">
        storage
      </mat-icon>
      <mat-icon 
        class="status-icon" 
        [class.active]="systemStatus.ai"
        matTooltip="AI Services">
        psychology
      </mat-icon>
    </div>
    
    <!-- User Menu -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-menu-button">
      <mat-icon>account_circle</mat-icon>
    </button>
    
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item (click)="toggleTheme()">
        <mat-icon>palette</mat-icon>
        <span>Toggle Theme</span>
      </button>
      <button mat-menu-item routerLink="/settings">
        <mat-icon>settings</mat-icon>
        <span>Settings</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <!-- Main Content Area -->
  <mat-sidenav-container class="sidenav-container">
    
    <!-- Side Navigation -->
    <mat-sidenav 
      #sidenav 
      [opened]="sidenavOpened" 
      mode="side" 
      class="cosmic-sidenav">
      
      <div class="sidenav-content">
        <!-- Navigation Header -->
        <div class="nav-header">
          <div class="nav-logo">
            <mat-icon class="logo-icon">auto_awesome</mat-icon>
            <span class="logo-text">Krabulon</span>
          </div>
          <div class="nav-subtitle">Live Interface</div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="nav-menu">
          <a mat-list-item 
             routerLink="/dashboard" 
             routerLinkActive="active"
             class="nav-item">
            <mat-icon matListItemIcon>dashboard</mat-icon>
            <span matListItemTitle>Dashboard</span>
          </a>
          
          <a mat-list-item 
             routerLink="/transcription" 
             routerLinkActive="active"
             class="nav-item">
            <mat-icon matListItemIcon>mic</mat-icon>
            <span matListItemTitle>Transcription Monitor</span>
          </a>
          
          <a mat-list-item 
             routerLink="/crm" 
             routerLinkActive="active"
             class="nav-item">
            <mat-icon matListItemIcon>people</mat-icon>
            <span matListItemTitle>CRM Management</span>
          </a>
          
          <a mat-list-item 
             routerLink="/equipment" 
             routerLinkActive="active"
             class="nav-item">
            <mat-icon matListItemIcon>build</mat-icon>
            <span matListItemTitle>Equipment Registry</span>
          </a>
          
          <a mat-list-item 
             routerLink="/python-mixer" 
             routerLinkActive="active"
             class="nav-item">
            <mat-icon matListItemIcon>code</mat-icon>
            <span matListItemTitle>Python Mixer</span>
          </a>
          
          <a mat-list-item 
             routerLink="/analytics" 
             routerLinkActive="active"
             class="nav-item">
            <mat-icon matListItemIcon>analytics</mat-icon>
            <span matListItemTitle>Analytics</span>
          </a>
          
          <mat-divider class="nav-divider"></mat-divider>
          
          <a mat-list-item 
             routerLink="/settings" 
             routerLinkActive="active"
             class="nav-item">
            <mat-icon matListItemIcon>settings</mat-icon>
            <span matListItemTitle>Settings</span>
          </a>
        </nav>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
          <h3 class="section-title">Quick Actions</h3>
          <button mat-stroked-button class="quick-action-btn" color="primary">
            <mat-icon>add</mat-icon>
            New Customer
          </button>
          <button mat-stroked-button class="quick-action-btn" color="accent">
            <mat-icon>upload</mat-icon>
            Upload Audio
          </button>
          <button mat-stroked-button class="quick-action-btn" color="warn">
            <mat-icon>bug_report</mat-icon>
            Report Issue
          </button>
        </div>
      </div>
    </mat-sidenav>

    <!-- Main Content -->
    <mat-sidenav-content class="main-content">
      <div class="content-wrapper">
        <!-- Breadcrumb Navigation -->
        <nav class="breadcrumb-nav" *ngIf="currentRoute !== '/dashboard'">
          <ol class="breadcrumb">
            <li><a routerLink="/dashboard">Dashboard</a></li>
            <li class="current">{{ currentRoute | titlecase }}</li>
          </ol>
        </nav>
        
        <!-- Router Outlet -->
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>

<!-- Floating Action Button -->
<button 
  mat-fab 
  class="floating-action-btn cosmic-glow" 
  color="primary"
  matTooltip="Quick Actions"
  [matMenuTriggerFor]="fabMenu">
  <mat-icon>add</mat-icon>
</button>

<mat-menu #fabMenu="matMenu" xPosition="before">
  <button mat-menu-item>
    <mat-icon>mic</mat-icon>
    <span>Start Recording</span>
  </button>
  <button mat-menu-item>
    <mat-icon>person_add</mat-icon>
    <span>Add Customer</span>
  </button>
  <button mat-menu-item>
    <mat-icon>build</mat-icon>
    <span>Add Equipment</span>
  </button>
</mat-menu>
