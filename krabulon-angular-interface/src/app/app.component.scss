// Cosmic Background
.cosmic-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.1;
  pointer-events: none;
}

// Loading Overlay
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--cosmic-primary), var(--cosmic-secondary));
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  
  .loading-content {
    text-align: center;
    color: white;
    
    .cosmic-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: cosmic-spin 1s ease-in-out infinite;
      margin: 0 auto 1rem;
    }
    
    .loading-text {
      font-size: 1.2rem;
      font-weight: 300;
      margin: 0;
    }
  }
}

@keyframes cosmic-spin {
  to { transform: rotate(360deg); }
}

// App Container
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

// Cosmic Toolbar
.cosmic-toolbar {
  background: linear-gradient(90deg, var(--cosmic-primary), var(--cosmic-secondary)) !important;
  color: white !important;
  box-shadow: var(--cosmic-shadow);
  z-index: 1000;
  
  .app-title {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 500;
    
    .title-icon {
      margin-right: 0.5rem;
      font-size: 1.8rem;
    }
  }
  
  .spacer {
    flex: 1 1 auto;
  }
  
  .system-health {
    display: flex;
    align-items: center;
    margin-right: 1rem;
    font-weight: 500;
    
    mat-icon {
      margin-right: 0.25rem;
    }
    
    .health-percentage {
      font-size: 0.9rem;
    }
  }
  
  .status-indicators {
    display: flex;
    gap: 0.5rem;
    margin-right: 1rem;
    
    .status-icon {
      opacity: 0.5;
      transition: all 0.3s ease;
      
      &.active {
        opacity: 1;
        color: var(--cosmic-success);
        text-shadow: 0 0 8px currentColor;
      }
    }
  }
  
  .menu-button,
  .user-menu-button {
    color: white !important;
  }
}

// Sidenav Container
.sidenav-container {
  flex: 1;
  background: var(--cosmic-background);
}

// Cosmic Sidenav
.cosmic-sidenav {
  width: 280px;
  background: var(--cosmic-surface) !important;
  border-right: 1px solid var(--cosmic-border);
  box-shadow: var(--cosmic-shadow);
  
  .sidenav-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0;
  }
  
  .nav-header {
    padding: 2rem 1.5rem 1rem;
    background: linear-gradient(135deg, var(--cosmic-primary), var(--cosmic-secondary));
    color: white;
    text-align: center;
    
    .nav-logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.5rem;
      
      .logo-icon {
        font-size: 2rem;
        margin-right: 0.5rem;
      }
      
      .logo-text {
        font-size: 1.5rem;
        font-weight: 600;
      }
    }
    
    .nav-subtitle {
      font-size: 0.9rem;
      opacity: 0.8;
      font-weight: 300;
    }
  }
  
  .nav-menu {
    flex: 1;
    padding: 1rem 0;
    
    .nav-item {
      margin: 0.25rem 1rem;
      border-radius: var(--cosmic-radius);
      transition: all 0.3s ease;
      color: var(--cosmic-text-primary);
      
      &:hover {
        background: rgba(99, 102, 241, 0.1);
        transform: translateX(4px);
      }
      
      &.active {
        background: linear-gradient(90deg, var(--cosmic-primary), var(--cosmic-secondary));
        color: white;
        box-shadow: var(--cosmic-shadow);
        
        mat-icon {
          color: white;
        }
      }
      
      mat-icon {
        color: var(--cosmic-primary);
        margin-right: 1rem;
      }
    }
    
    .nav-divider {
      margin: 1rem;
      background: var(--cosmic-border);
    }
  }
  
  .quick-actions {
    padding: 1rem 1.5rem 2rem;
    border-top: 1px solid var(--cosmic-border);
    
    .section-title {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--cosmic-text-secondary);
      margin: 0 0 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .quick-action-btn {
      width: 100%;
      margin-bottom: 0.5rem;
      justify-content: flex-start;
      
      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// Main Content
.main-content {
  background: var(--cosmic-background);
  
  .content-wrapper {
    padding: 1.5rem;
    min-height: calc(100vh - 64px);
  }
  
  .breadcrumb-nav {
    margin-bottom: 1.5rem;
    
    .breadcrumb {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--cosmic-text-secondary);
      
      li {
        &:not(:last-child)::after {
          content: '/';
          margin: 0 0.5rem;
          color: var(--cosmic-border);
        }
        
        a {
          color: var(--cosmic-primary);
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        &.current {
          color: var(--cosmic-text-primary);
          font-weight: 500;
        }
      }
    }
  }
}

// Floating Action Button
.floating-action-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  background: linear-gradient(135deg, var(--cosmic-primary), var(--cosmic-secondary)) !important;
  
  &:hover {
    transform: scale(1.1);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .cosmic-toolbar {
    .system-health,
    .status-indicators {
      display: none;
    }
    
    .app-title {
      font-size: 1.2rem;
      
      .title-icon {
        font-size: 1.5rem;
      }
    }
  }
  
  .cosmic-sidenav {
    width: 100%;
  }
  
  .main-content .content-wrapper {
    padding: 1rem;
  }
  
  .floating-action-btn {
    bottom: 1rem;
    right: 1rem;
  }
}

// Dark Theme Support
.dark-theme {
  .cosmic-sidenav {
    background: var(--cosmic-dark-surface) !important;
    border-right-color: var(--cosmic-dark-border);
    
    .nav-item {
      color: var(--cosmic-dark-text-primary);
      
      &:hover {
        background: rgba(99, 102, 241, 0.2);
      }
    }
    
    .quick-actions {
      border-top-color: var(--cosmic-dark-border);
      
      .section-title {
        color: var(--cosmic-dark-text-secondary);
      }
    }
  }
  
  .main-content {
    background: var(--cosmic-dark-background);
    
    .breadcrumb {
      color: var(--cosmic-dark-text-secondary);
      
      li.current {
        color: var(--cosmic-dark-text-primary);
      }
    }
  }
}
