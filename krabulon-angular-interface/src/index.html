<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Krabulon Live Interface - Advanced HVAC CRM</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <meta name="description" content="Advanced Angular interface for HVAC CRM and Python Mixer with real-time transcription monitoring">
  <meta name="keywords" content="HVAC, CRM, Angular, Transcription, Python Mixer, Krabulon">
  <meta name="author" content="HVAC CRM Team">
  
  <!-- Cosmic Design System CSS Variables -->
  <style>
    :root {
      --cosmic-primary: #6366f1;
      --cosmic-secondary: #8b5cf6;
      --cosmic-accent: #06b6d4;
      --cosmic-success: #10b981;
      --cosmic-warning: #f59e0b;
      --cosmic-error: #ef4444;
      --cosmic-surface: #ffffff;
      --cosmic-background: #f8fafc;
      --cosmic-text-primary: #1e293b;
      --cosmic-text-secondary: #64748b;
      --cosmic-border: #e2e8f0;
      --cosmic-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --cosmic-radius: 8px;
      --cosmic-spacing: 1rem;
      
      /* Dark mode variables */
      --cosmic-dark-surface: #1e293b;
      --cosmic-dark-background: #0f172a;
      --cosmic-dark-text-primary: #f1f5f9;
      --cosmic-dark-text-secondary: #94a3b8;
      --cosmic-dark-border: #334155;
    }
    
    /* Loading animation */
    .cosmic-loader {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: linear-gradient(135deg, var(--cosmic-primary), var(--cosmic-secondary));
    }
    
    .cosmic-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: cosmic-spin 1s ease-in-out infinite;
    }
    
    @keyframes cosmic-spin {
      to { transform: rotate(360deg); }
    }
    
    /* Cosmic glow effect */
    .cosmic-glow {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
      transition: box-shadow 0.3s ease;
    }
    
    .cosmic-glow:hover {
      box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
    }
  </style>
</head>
<body class="mat-typography">
  <app-root>
    <!-- Loading screen -->
    <div class="cosmic-loader">
      <div class="cosmic-spinner"></div>
    </div>
  </app-root>
</body>
</html>
