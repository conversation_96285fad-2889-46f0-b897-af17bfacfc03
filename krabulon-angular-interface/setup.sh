#!/bin/bash

# 🚀 Krabulon Angular Interface Setup Script
# Advanced Angular Interface for HVAC CRM and Python Mixer

echo "🌟 Setting up Krabulon Angular Interface..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed."
    exit 1
fi

echo "✅ npm version: $(npm -v)"

# Install Angular CLI globally if not present
if ! command -v ng &> /dev/null; then
    echo "📦 Installing Angular CLI globally..."
    npm install -g @angular/cli@17
else
    echo "✅ Angular CLI is already installed: $(ng version --quiet)"
fi

# Create directory structure
echo "📁 Creating directory structure..."

mkdir -p src/app/{core,shared,features,layouts}
mkdir -p src/app/core/{services,guards,interceptors,models}
mkdir -p src/app/shared/{components,directives,pipes,utils}
mkdir -p src/app/features/{dashboard,transcription,crm,equipment,analytics,settings,python-mixer}
mkdir -p src/app/layouts/{main-layout,auth-layout}
mkdir -p src/assets/{images,icons,fonts,data}
mkdir -p src/environments

echo "✅ Directory structure created"

# Create TypeScript configuration files
echo "📝 Creating TypeScript configuration files..."

cat > tsconfig.app.json << 'EOF'
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "./out-tsc/app",
    "types": []
  },
  "files": [
    "src/main.ts"
  ],
  "include": [
    "src/**/*.d.ts"
  ]
}
EOF

cat > tsconfig.spec.json << 'EOF'
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "./out-tsc/spec",
    "types": [
      "jasmine"
    ]
  },
  "include": [
    "src/**/*.spec.ts",
    "src/**/*.d.ts"
  ]
}
EOF

echo "✅ TypeScript configuration files created"

# Create environment files
echo "🌍 Creating environment files..."

cat > src/environments/environment.ts << 'EOF'
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080/api',
  wsUrl: 'ws://localhost:8080/ws',
  pythonMixerUrl: 'http://localhost:8000',
  transcriptionUrl: 'http://localhost:8765',
  enableDebug: true,
  version: '1.0.0'
};
EOF

cat > src/environments/environment.prod.ts << 'EOF'
export const environment = {
  production: true,
  apiUrl: 'https://api.krabulon.com',
  wsUrl: 'wss://api.krabulon.com/ws',
  pythonMixerUrl: 'https://mixer.krabulon.com',
  transcriptionUrl: 'https://transcription.krabulon.com',
  enableDebug: false,
  version: '1.0.0'
};
EOF

echo "✅ Environment files created"

# Create main.ts
echo "🎯 Creating main application files..."

cat > src/main.ts << 'EOF'
import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';

bootstrapApplication(AppComponent, appConfig)
  .catch((err) => console.error(err));
EOF

# Create app.config.ts
cat > src/app/app.config.ts << 'EOF'
import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideAnimations(),
    provideHttpClient(withInterceptorsFromDi()),
    importProvidersFrom(MatSnackBarModule)
  ]
};
EOF

# Create app.routes.ts
cat > src/app/app.routes.ts << 'EOF'
import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },
  {
    path: 'transcription',
    loadComponent: () => import('./features/transcription/transcription.component').then(m => m.TranscriptionComponent)
  },
  {
    path: 'crm',
    loadChildren: () => import('./features/crm/crm.routes').then(m => m.crmRoutes)
  },
  {
    path: 'equipment',
    loadChildren: () => import('./features/equipment/equipment.routes').then(m => m.equipmentRoutes)
  },
  {
    path: 'python-mixer',
    loadComponent: () => import('./features/python-mixer/python-mixer.component').then(m => m.PythonMixerComponent)
  },
  {
    path: 'analytics',
    loadComponent: () => import('./features/analytics/analytics.component').then(m => m.AnalyticsComponent)
  },
  {
    path: 'settings',
    loadComponent: () => import('./features/settings/settings.component').then(m => m.SettingsComponent)
  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
EOF

echo "✅ Main application files created"

# Create global styles
echo "🎨 Creating global styles..."

cat > src/styles.scss << 'EOF'
/* Global Styles for Krabulon Angular Interface */

@import '@angular/material/theming';
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Include the common styles for Angular Material */
@include mat-core();

/* Define custom theme */
$krabulon-primary: mat-palette($mat-indigo);
$krabulon-accent: mat-palette($mat-cyan);
$krabulon-warn: mat-palette($mat-red);

$krabulon-theme: mat-light-theme((
  color: (
    primary: $krabulon-primary,
    accent: $krabulon-accent,
    warn: $krabulon-warn,
  )
));

@include angular-material-theme($krabulon-theme);

/* Global CSS Reset and Base Styles */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  background: var(--cosmic-background, #f8fafc);
  color: var(--cosmic-text-primary, #1e293b);
}

/* Cosmic Design System Variables */
:root {
  --cosmic-primary: #6366f1;
  --cosmic-secondary: #8b5cf6;
  --cosmic-accent: #06b6d4;
  --cosmic-success: #10b981;
  --cosmic-warning: #f59e0b;
  --cosmic-error: #ef4444;
  --cosmic-surface: #ffffff;
  --cosmic-background: #f8fafc;
  --cosmic-text-primary: #1e293b;
  --cosmic-text-secondary: #64748b;
  --cosmic-border: #e2e8f0;
  --cosmic-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --cosmic-radius: 8px;
  --cosmic-spacing: 1rem;
}

/* Utility Classes */
.cosmic-glow {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  transition: box-shadow 0.3s ease;
}

.cosmic-glow:hover {
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--cosmic-background);
}

::-webkit-scrollbar-thumb {
  background: var(--cosmic-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--cosmic-text-secondary);
}
EOF

echo "✅ Global styles created"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

echo "🎉 Krabulon Angular Interface setup completed!"
echo ""
echo "🚀 To start the development server:"
echo "   npm start"
echo ""
echo "🔧 To build for production:"
echo "   npm run build:prod"
echo ""
echo "📊 Dashboard will be available at: http://localhost:4200"
echo "🎤 Transcription monitoring: http://localhost:4200/transcription"
echo "👥 CRM interface: http://localhost:4200/crm"
echo "🔧 Equipment registry: http://localhost:4200/equipment"
echo "🐍 Python Mixer: http://localhost:4200/python-mixer"
echo ""
echo "✨ Happy coding with Krabulon Live Interface!"
