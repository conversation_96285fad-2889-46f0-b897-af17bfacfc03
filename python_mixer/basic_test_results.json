{"timestamp": "2025-05-30T21:47:25.754124", "tests": {"docker_services": {"nemo_stt": {"available": true, "status_code": 200, "url": "http://localhost:8889/health"}, "transcription_orchestrator": {"available": true, "status_code": 200, "url": "http://localhost:9000/health"}, "gemma_integration": {"available": true, "status_code": 200, "url": "http://*************:1234/v1/models"}}, "email_connection": {"success": false, "error": "b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'"}, "transcription_service": {"available": true, "status": 200}}}