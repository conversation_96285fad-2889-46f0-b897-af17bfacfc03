{"timestamp": "2025-05-30T21:49:16.257188", "tests": {"nemo_health": {"status": 200, "healthy": true, "response": "{\"status\":\"healthy\",\"timestamp\":\"2025-05-30T19:49:16.260517\",\"models_loaded\":[\"simple_mock\"],\"gpu_available\":false,\"gpu_count\":0,\"mode\":\"testing\"}"}, "orchestrator_health": {"status": 200, "healthy": true, "response": "{\"status\":\"healthy\",\"timestamp\":\"2025-05-30T19:49:16.263654\",\"services\":{\"nvidia_stt\":\"http://simple-stt:8889\",\"audio_converter\":\"http://audio-converter:8080\",\"email_processor\":\"http://email-processor:8080\",\"gemma_integration\":\"http://gemma-integration:8080\",\"gobackend\":\"http://host.docker.internal:8080\"},\"active_jobs\":0,\"queue_size\":0,\"websocket_connections\":0}"}, "gemma_models": {"status": 200, "healthy": true, "models": 4}, "transcription_endpoint": {"status": 422, "responsive": true, "message": "Endpoint working, validation error expected"}, "orchestrator_status": {"status": 404, "working": false, "response": "{\"detail\":\"Not Found\"}"}}}