#!/usr/bin/env python3
"""
🎯 SIMPLE TRANSCRIPTION TESTING SYSTEM
Basic testing of email connection and transcription pipeline
"""

import asyncio
import json
import time
import email
import imaplib
import ssl
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import tempfile
import os

class SimpleTranscriptionTester:
    """🎯 Simple transcription testing system"""
    
    def __init__(self):
        # Email <NAME_EMAIL>
        self.email_config = {
            "host": "imap.gmail.com",
            "port": 993,
            "username": "<EMAIL>", 
            "password": "Blaeritipol1",
            "use_ssl": True
        }
        
        self.test_results = {
            "email_processing": [],
            "transcription_tests": [],
            "system_health": {}
        }
    
    def test_email_connection(self) -> Dict[str, Any]:
        """Test <NAME_EMAIL> email account"""
        print("🔍 Testing email <NAME_EMAIL>...")
        
        test_result = {
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "connection_time": 0.0,
            "folders_found": [],
            "total_emails": 0,
            "m4a_attachments": 0,
            "error": None
        }
        
        start_time = time.time()
        
        try:
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to server
            connection = imaplib.IMAP4_SSL(self.email_config["host"], self.email_config["port"], ssl_context=context)
            connection.login(self.email_config["username"], self.email_config["password"])
            connection.select('INBOX')
            
            test_result["connection_time"] = time.time() - start_time
            test_result["success"] = True
            
            # List folders
            status, folders = connection.list()
            test_result["folders_found"] = [folder.decode().split('"')[-2] for folder in folders] if folders else []
            
            # Get total messages
            status, messages = connection.search(None, 'ALL')
            total_messages = len(messages[0].split()) if messages[0] else 0
            test_result["total_emails"] = total_messages
            
            # Check for M4A attachments in recent emails
            m4a_count = 0
            email_ids = messages[0].split()
            
            for email_id in email_ids[-10:]:  # Check last 10 emails
                try:
                    status, msg_data = connection.fetch(email_id, '(RFC822)')
                    if msg_data and msg_data[0]:
                        raw_email = msg_data[0][1].decode('utf-8', errors='ignore')
                        if self._has_m4a_attachment(raw_email):
                            m4a_count += 1
                except:
                    continue
            
            test_result["m4a_attachments"] = m4a_count
            
            connection.close()
            connection.logout()
            
            print(f"✅ Email connection successful! Found {m4a_count} M4A attachments")
            
        except Exception as e:
            test_result["error"] = str(e)
            test_result["connection_time"] = time.time() - start_time
            print(f"❌ Email connection error: {e}")
        
        self.test_results["email_processing"].append(test_result)
        return test_result
    
    def _has_m4a_attachment(self, raw_email: str) -> bool:
        """Check if email has M4A attachment"""
        try:
            msg = email.message_from_string(raw_email)
            for part in msg.walk():
                if part.get_content_disposition() == 'attachment':
                    filename = part.get_filename()
                    if filename and filename.lower().endswith('.m4a'):
                        return True
            return False
        except Exception:
            return False
    
    def test_system_health(self) -> Dict[str, Any]:
        """Test overall system health"""
        print("🏥 Testing system health...")
        
        health_result = {
            "timestamp": datetime.now().isoformat(),
            "email_system": False,
            "transcription_service": False,
            "cosmic_interface": False,
            "overall_health": "unhealthy"
        }
        
        # Test email system
        email_test = self.test_email_connection()
        health_result["email_system"] = email_test["success"]
        
        # Test transcription service (placeholder)
        try:
            # This would test the actual transcription service
            health_result["transcription_service"] = False  # Set to True when service is available
        except:
            health_result["transcription_service"] = False
        
        # Test cosmic interface (placeholder)
        try:
            # This would test the cosmic interface
            health_result["cosmic_interface"] = False  # Set to True when interface is available
        except:
            health_result["cosmic_interface"] = False
        
        # Determine overall health
        if health_result["email_system"]:
            health_result["overall_health"] = "partial"
        if all([health_result["email_system"], health_result["transcription_service"], health_result["cosmic_interface"]]):
            health_result["overall_health"] = "healthy"
        
        self.test_results["system_health"] = health_result
        return health_result
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate test report"""
        print("📋 Generating test report...")
        
        report = {
            "test_summary": {
                "timestamp": datetime.now().isoformat(),
                "total_tests_executed": len(self.test_results.get("email_processing", [])),
                "overall_success": False
            },
            "email_processing_results": self.test_results.get("email_processing", []),
            "transcription_results": self.test_results.get("transcription_tests", []),
            "system_health": self.test_results.get("system_health", {}),
            "recommendations": []
        }
        
        # Add recommendations
        if self.test_results.get("system_health", {}).get("email_system"):
            report["recommendations"].append("✅ Email system is functional")
        else:
            report["recommendations"].append("❌ Email system needs attention")
        
        if not self.test_results.get("system_health", {}).get("transcription_service"):
            report["recommendations"].append("🔧 Transcription service needs setup")
        
        if not self.test_results.get("system_health", {}).get("cosmic_interface"):
            report["recommendations"].append("🌟 Cosmic interface needs integration")
        
        # Save report
        report_file = Path("simple_transcription_test_report.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 Report saved to: {report_file}")
        return report

def main():
    """Main test execution"""
    print("🚀 Starting Simple Transcription Testing...")
    
    tester = SimpleTranscriptionTester()
    
    # Run tests
    health_result = tester.test_system_health()
    
    # Generate report
    report = tester.generate_report()
    
    print("\n📊 Test Summary:")
    print(f"Email System: {'✅' if health_result['email_system'] else '❌'}")
    print(f"Transcription Service: {'✅' if health_result['transcription_service'] else '❌'}")
    print(f"Cosmic Interface: {'✅' if health_result['cosmic_interface'] else '❌'}")
    print(f"Overall Health: {health_result['overall_health']}")
    
    return report

if __name__ == "__main__":
    main()
