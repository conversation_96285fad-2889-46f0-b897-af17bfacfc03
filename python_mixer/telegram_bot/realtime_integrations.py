"""
Real-time Integrations for Telegram Bot
WebSocket connections, live data feeds, and real-time notifications
"""

import asyncio
import json
import websockets
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import aiohttp
from loguru import logger


@dataclass
class RealTimeEvent:
    """Real-time event structure."""
    event_type: str
    source: str
    data: Dict[str, Any]
    timestamp: datetime
    priority: str  # low, medium, high, critical
    user_id: Optional[int] = None
    chat_id: Optional[int] = None


@dataclass
class SystemAlert:
    """System alert structure."""
    alert_id: str
    severity: str  # info, warning, error, critical
    title: str
    message: str
    source_component: str
    timestamp: datetime
    auto_resolve: bool = False
    resolved: bool = False


class RealTimeIntegrations:
    """
    Real-time integrations manager for Telegram Bot.
    
    Features:
    - Live system monitoring
    - Equipment status updates
    - Customer interaction tracking
    - Performance metrics streaming
    - Alert management
    - WebSocket connections
    """
    
    def __init__(self, bot_manager):
        self.bot_manager = bot_manager
        self.active_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.event_subscribers: Dict[str, List[Callable]] = {}
        self.alert_queue: List[SystemAlert] = []
        self.metrics_buffer: List[Dict[str, Any]] = []
        
        # Real-time data sources
        self.data_sources = {
            "system_metrics": "ws://localhost:8765/metrics",
            "equipment_status": "ws://localhost:8766/equipment",
            "customer_activity": "ws://localhost:8767/activity",
            "crawl_progress": "ws://localhost:8768/crawling"
        }
        
        # Event handlers
        self.event_handlers = {
            "equipment_alert": self._handle_equipment_alert,
            "system_warning": self._handle_system_warning,
            "crawl_complete": self._handle_crawl_complete,
            "customer_message": self._handle_customer_message,
            "performance_threshold": self._handle_performance_threshold
        }
        
        self.is_running = False
        self.websocket_tasks: List[asyncio.Task] = []
        
        logger.info("⚡ Real-time integrations initialized")
    
    async def start(self):
        """Start real-time integrations."""
        try:
            logger.info("🚀 Starting real-time integrations...")
            
            self.is_running = True
            
            # Start WebSocket connections
            for source_name, ws_url in self.data_sources.items():
                task = asyncio.create_task(
                    self._connect_websocket(source_name, ws_url)
                )
                self.websocket_tasks.append(task)
            
            # Start alert processor
            alert_task = asyncio.create_task(self._process_alerts())
            self.websocket_tasks.append(alert_task)
            
            # Start metrics collector
            metrics_task = asyncio.create_task(self._collect_metrics())
            self.websocket_tasks.append(metrics_task)
            
            # Start notification sender
            notification_task = asyncio.create_task(self._send_notifications())
            self.websocket_tasks.append(notification_task)
            
            logger.success("✅ Real-time integrations started")
            
        except Exception as e:
            logger.error(f"Failed to start real-time integrations: {e}")
    
    async def stop(self):
        """Stop real-time integrations."""
        try:
            logger.info("🔄 Stopping real-time integrations...")
            
            self.is_running = False
            
            # Cancel all tasks
            for task in self.websocket_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(*self.websocket_tasks, return_exceptions=True)
            
            # Close WebSocket connections
            for connection in self.active_connections.values():
                await connection.close()
            
            self.active_connections.clear()
            self.websocket_tasks.clear()
            
            logger.success("✅ Real-time integrations stopped")
            
        except Exception as e:
            logger.error(f"Error stopping real-time integrations: {e}")
    
    async def emit_event(self, event: RealTimeEvent):
        """Emit real-time event to subscribers."""
        try:
            event_type = event.event_type
            
            # Process event through handlers
            if event_type in self.event_handlers:
                await self.event_handlers[event_type](event)
            
            # Notify subscribers
            if event_type in self.event_subscribers:
                for callback in self.event_subscribers[event_type]:
                    try:
                        await callback(event)
                    except Exception as e:
                        logger.error(f"Event subscriber error: {e}")
            
            # Send to Telegram if user/chat specified
            if event.user_id or event.chat_id:
                await self._send_telegram_notification(event)
            
        except Exception as e:
            logger.error(f"Event emission failed: {e}")
    
    async def subscribe_to_events(self, event_type: str, callback: Callable):
        """Subscribe to real-time events."""
        if event_type not in self.event_subscribers:
            self.event_subscribers[event_type] = []
        
        self.event_subscribers[event_type].append(callback)
        logger.info(f"📡 Subscribed to {event_type} events")
    
    async def create_alert(
        self,
        severity: str,
        title: str,
        message: str,
        source_component: str,
        auto_resolve: bool = False
    ) -> str:
        """Create system alert."""
        alert_id = f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        alert = SystemAlert(
            alert_id=alert_id,
            severity=severity,
            title=title,
            message=message,
            source_component=source_component,
            timestamp=datetime.now(),
            auto_resolve=auto_resolve
        )
        
        self.alert_queue.append(alert)
        
        # Emit alert event
        await self.emit_event(RealTimeEvent(
            event_type="system_alert",
            source="alert_manager",
            data=asdict(alert),
            timestamp=datetime.now(),
            priority=severity
        ))
        
        logger.warning(f"🚨 Alert created: {title} ({severity})")
        return alert_id
    
    async def resolve_alert(self, alert_id: str):
        """Resolve system alert."""
        for alert in self.alert_queue:
            if alert.alert_id == alert_id:
                alert.resolved = True
                logger.info(f"✅ Alert resolved: {alert_id}")
                break
    
    async def get_live_metrics(self) -> Dict[str, Any]:
        """Get current live metrics."""
        try:
            current_time = datetime.now()
            
            # Get bot metrics
            bot_status = await self.bot_manager.get_status()
            
            # Calculate real-time metrics
            recent_metrics = [
                m for m in self.metrics_buffer 
                if datetime.fromisoformat(m["timestamp"]) > current_time - timedelta(minutes=5)
            ]
            
            avg_response_time = 0
            if recent_metrics:
                response_times = [m.get("response_time", 0) for m in recent_metrics]
                avg_response_time = sum(response_times) / len(response_times)
            
            return {
                "timestamp": current_time.isoformat(),
                "bot_status": bot_status,
                "active_connections": len(self.active_connections),
                "pending_alerts": len([a for a in self.alert_queue if not a.resolved]),
                "metrics_buffer_size": len(self.metrics_buffer),
                "avg_response_time_5min": avg_response_time,
                "event_subscribers": {
                    event_type: len(callbacks) 
                    for event_type, callbacks in self.event_subscribers.items()
                },
                "realtime_sources": {
                    source: "connected" if source in self.active_connections else "disconnected"
                    for source in self.data_sources.keys()
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get live metrics: {e}")
            return {"error": str(e)}
    
    async def _connect_websocket(self, source_name: str, ws_url: str):
        """Connect to WebSocket data source."""
        while self.is_running:
            try:
                logger.info(f"🔌 Connecting to {source_name}: {ws_url}")
                
                async with websockets.connect(ws_url) as websocket:
                    self.active_connections[source_name] = websocket
                    logger.success(f"✅ Connected to {source_name}")
                    
                    async for message in websocket:
                        if not self.is_running:
                            break
                        
                        try:
                            data = json.loads(message)
                            await self._process_websocket_data(source_name, data)
                        except json.JSONDecodeError:
                            logger.warning(f"Invalid JSON from {source_name}: {message}")
                        except Exception as e:
                            logger.error(f"Error processing {source_name} data: {e}")
                            
            except Exception as e:
                logger.warning(f"WebSocket connection failed for {source_name}: {e}")
                
                # Remove from active connections
                if source_name in self.active_connections:
                    del self.active_connections[source_name]
                
                # Retry after delay
                if self.is_running:
                    await asyncio.sleep(5)
    
    async def _process_websocket_data(self, source: str, data: Dict[str, Any]):
        """Process incoming WebSocket data."""
        try:
            # Add timestamp if not present
            if "timestamp" not in data:
                data["timestamp"] = datetime.now().isoformat()
            
            # Create real-time event
            event = RealTimeEvent(
                event_type=data.get("event_type", "data_update"),
                source=source,
                data=data,
                timestamp=datetime.now(),
                priority=data.get("priority", "low")
            )
            
            # Emit event
            await self.emit_event(event)
            
            # Store in metrics buffer
            if source == "system_metrics":
                self.metrics_buffer.append(data)
                
                # Keep buffer size manageable
                if len(self.metrics_buffer) > 1000:
                    self.metrics_buffer = self.metrics_buffer[-500:]
            
        except Exception as e:
            logger.error(f"WebSocket data processing error: {e}")
    
    async def _process_alerts(self):
        """Process alert queue."""
        while self.is_running:
            try:
                # Process pending alerts
                current_time = datetime.now()
                
                for alert in self.alert_queue[:]:  # Copy list to avoid modification during iteration
                    if alert.resolved:
                        continue
                    
                    # Auto-resolve old alerts if configured
                    if alert.auto_resolve and current_time - alert.timestamp > timedelta(hours=1):
                        alert.resolved = True
                        logger.info(f"🔄 Auto-resolved alert: {alert.alert_id}")
                    
                    # Send critical alerts immediately
                    if alert.severity == "critical" and not alert.resolved:
                        await self._send_critical_alert(alert)
                
                # Clean up old resolved alerts
                self.alert_queue = [
                    alert for alert in self.alert_queue 
                    if not alert.resolved or current_time - alert.timestamp < timedelta(days=1)
                ]
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Alert processing error: {e}")
                await asyncio.sleep(30)
    
    async def _collect_metrics(self):
        """Collect system metrics."""
        while self.is_running:
            try:
                # Collect current metrics
                metrics = {
                    "timestamp": datetime.now().isoformat(),
                    "memory_usage": await self._get_memory_usage(),
                    "cpu_usage": await self._get_cpu_usage(),
                    "active_users": await self._get_active_users(),
                    "response_time": await self._measure_response_time(),
                    "error_rate": await self._calculate_error_rate()
                }
                
                # Emit metrics event
                await self.emit_event(RealTimeEvent(
                    event_type="metrics_update",
                    source="metrics_collector",
                    data=metrics,
                    timestamp=datetime.now(),
                    priority="low"
                ))
                
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                logger.error(f"Metrics collection error: {e}")
                await asyncio.sleep(60)
    
    async def _send_notifications(self):
        """Send real-time notifications."""
        while self.is_running:
            try:
                # Check for high-priority events that need immediate notification
                current_time = datetime.now()
                
                # Send daily summary to admins
                if current_time.hour == 9 and current_time.minute == 0:
                    await self._send_daily_summary()
                
                # Send weekly reports
                if current_time.weekday() == 0 and current_time.hour == 9:  # Monday 9 AM
                    await self._send_weekly_report()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Notification sending error: {e}")
                await asyncio.sleep(300)
    
    async def _handle_equipment_alert(self, event: RealTimeEvent):
        """Handle equipment alert events."""
        data = event.data
        equipment_id = data.get("equipment_id")
        alert_type = data.get("alert_type")
        
        logger.warning(f"🔧 Equipment alert: {equipment_id} - {alert_type}")
        
        # Create system alert
        await self.create_alert(
            severity="warning",
            title=f"Equipment Alert: {equipment_id}",
            message=f"Alert type: {alert_type}. Immediate attention required.",
            source_component="equipment_monitor"
        )
    
    async def _handle_system_warning(self, event: RealTimeEvent):
        """Handle system warning events."""
        data = event.data
        component = data.get("component")
        warning_type = data.get("warning_type")
        
        logger.warning(f"⚠️ System warning: {component} - {warning_type}")
    
    async def _handle_crawl_complete(self, event: RealTimeEvent):
        """Handle crawl completion events."""
        data = event.data
        manufacturer = data.get("manufacturer")
        equipment_count = data.get("equipment_count", 0)
        
        logger.info(f"🕷️ Crawl completed: {manufacturer} - {equipment_count} items")
        
        # Notify interested users
        notification_text = f"🎉 Crawl completed for {manufacturer}! Found {equipment_count} equipment items."
        
        # Send to admin users
        for admin_id in self.bot_manager.config.admin_user_ids:
            try:
                await self.bot_manager.bot.send_message(
                    chat_id=admin_id,
                    text=notification_text
                )
            except Exception as e:
                logger.error(f"Failed to send notification to admin {admin_id}: {e}")
    
    async def _handle_customer_message(self, event: RealTimeEvent):
        """Handle customer message events."""
        data = event.data
        user_id = data.get("user_id")
        message_type = data.get("message_type")
        
        logger.info(f"💬 Customer message: {user_id} - {message_type}")
    
    async def _handle_performance_threshold(self, event: RealTimeEvent):
        """Handle performance threshold events."""
        data = event.data
        metric = data.get("metric")
        threshold = data.get("threshold")
        current_value = data.get("current_value")
        
        logger.warning(f"📊 Performance threshold exceeded: {metric} = {current_value} (threshold: {threshold})")
        
        await self.create_alert(
            severity="warning",
            title=f"Performance Threshold Exceeded",
            message=f"{metric} is {current_value}, exceeding threshold of {threshold}",
            source_component="performance_monitor"
        )
    
    async def _send_telegram_notification(self, event: RealTimeEvent):
        """Send event notification to Telegram."""
        try:
            if event.priority in ["high", "critical"]:
                message = f"🚨 {event.event_type.replace('_', ' ').title()}\n\n"
                message += f"Source: {event.source}\n"
                message += f"Time: {event.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
                message += f"Priority: {event.priority.upper()}\n\n"
                
                # Add relevant data
                if "message" in event.data:
                    message += event.data["message"]
                
                # Send to specified user/chat or admins
                target_ids = []
                if event.user_id:
                    target_ids.append(event.user_id)
                elif event.chat_id:
                    target_ids.append(event.chat_id)
                else:
                    target_ids = self.bot_manager.config.admin_user_ids
                
                for target_id in target_ids:
                    try:
                        await self.bot_manager.bot.send_message(
                            chat_id=target_id,
                            text=message
                        )
                    except Exception as e:
                        logger.error(f"Failed to send notification to {target_id}: {e}")
                        
        except Exception as e:
            logger.error(f"Telegram notification failed: {e}")
    
    async def _send_critical_alert(self, alert: SystemAlert):
        """Send critical alert to all admins."""
        try:
            message = f"🚨 CRITICAL ALERT 🚨\n\n"
            message += f"Title: {alert.title}\n"
            message += f"Component: {alert.source_component}\n"
            message += f"Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            message += f"Message: {alert.message}\n\n"
            message += f"Alert ID: {alert.alert_id}"
            
            for admin_id in self.bot_manager.config.admin_user_ids:
                try:
                    await self.bot_manager.bot.send_message(
                        chat_id=admin_id,
                        text=message
                    )
                except Exception as e:
                    logger.error(f"Failed to send critical alert to admin {admin_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Critical alert sending failed: {e}")
    
    # Placeholder methods for metrics collection
    async def _get_memory_usage(self) -> float:
        """Get current memory usage percentage."""
        import psutil
        return psutil.virtual_memory().percent
    
    async def _get_cpu_usage(self) -> float:
        """Get current CPU usage percentage."""
        import psutil
        return psutil.cpu_percent(interval=1)
    
    async def _get_active_users(self) -> int:
        """Get number of active users."""
        # This would query the database for recent user activity
        return len(self.bot_manager.config.admin_user_ids)  # Placeholder
    
    async def _measure_response_time(self) -> float:
        """Measure average response time."""
        # This would measure actual response times
        return 0.5  # Placeholder
    
    async def _calculate_error_rate(self) -> float:
        """Calculate current error rate."""
        bot_status = await self.bot_manager.get_status()
        bot_info = bot_status.get("bot_status", {})
        
        total_messages = bot_info.get("message_count", 1)
        error_count = bot_info.get("error_count", 0)
        
        return (error_count / total_messages) * 100 if total_messages > 0 else 0
    
    async def _send_daily_summary(self):
        """Send daily summary to admins."""
        try:
            metrics = await self.get_live_metrics()
            
            summary = f"📊 Daily Summary - {datetime.now().strftime('%Y-%m-%d')}\n\n"
            summary += f"• Active Connections: {metrics.get('active_connections', 0)}\n"
            summary += f"• Pending Alerts: {metrics.get('pending_alerts', 0)}\n"
            summary += f"• Avg Response Time: {metrics.get('avg_response_time_5min', 0):.2f}s\n"
            summary += f"• System Status: Operational ✅\n\n"
            summary += "Have a great day! 🌟"
            
            for admin_id in self.bot_manager.config.admin_user_ids:
                try:
                    await self.bot_manager.bot.send_message(
                        chat_id=admin_id,
                        text=summary
                    )
                except Exception as e:
                    logger.error(f"Failed to send daily summary to admin {admin_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Daily summary sending failed: {e}")
    
    async def _send_weekly_report(self):
        """Send weekly report to admins."""
        try:
            report = f"📈 Weekly Report - Week of {datetime.now().strftime('%Y-%m-%d')}\n\n"
            report += "🎉 System has been running smoothly!\n"
            report += "📊 Performance metrics within normal ranges\n"
            report += "🔧 All integrations operational\n\n"
            report += "Thank you for using HVAC CRM Bot! 🚀"
            
            for admin_id in self.bot_manager.config.admin_user_ids:
                try:
                    await self.bot_manager.bot.send_message(
                        chat_id=admin_id,
                        text=report
                    )
                except Exception as e:
                    logger.error(f"Failed to send weekly report to admin {admin_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Weekly report sending failed: {e}")
