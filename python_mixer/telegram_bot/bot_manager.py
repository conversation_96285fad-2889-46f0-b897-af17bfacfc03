"""
Telegram Bot Manager for HVAC CRM System
Integrates with Krabulon deep crawling agent and MCP memory
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from aiogram import <PERSON><PERSON>, Dispatcher, Router
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode
from aiogram.webhook.aiohttp_server import SimpleRequestHandler, setup_application
from aiohttp import web
from loguru import logger

# Add parent directory for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from .config import BotConfig
from .handlers import setup_handlers
from .middleware import setup_middleware
from .krabulon_integration import KrabulonIntegration
from .ai_engine import AdvancedAIEngine
from .realtime_integrations import RealTimeIntegrations
from core.mcp_integration.mcp_manager import MCPManager


class TelegramBotManager:
    """
    Main Telegram Bot Manager for HVAC CRM System.
    
    Features:
    - Krabulon deep crawling integration
    - MCP memory persistence
    - HVAC equipment research
    - Document processing
    - Real-time system monitoring
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.config.validate()
        
        # Initialize bot and dispatcher
        self.bot = Bot(
            token=config.bot_token,
            default=DefaultBotProperties(parse_mode=ParseMode.HTML)
        )
        self.dp = Dispatcher()
        self.router = Router()
        
        # Integration components
        self.mcp_manager: Optional[MCPManager] = None
        self.krabulon_integration: Optional[KrabulonIntegration] = None
        self.ai_engine: Optional[AdvancedAIEngine] = None
        self.realtime_integrations: Optional[RealTimeIntegrations] = None
        
        # Runtime state
        self.is_running = False
        self.start_time: Optional[datetime] = None
        self.message_count = 0
        self.error_count = 0
        
        # Setup logging
        self._setup_logging()
        
        logger.info("🤖 Telegram Bot Manager initialized")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logger.remove()  # Remove default handler
        
        # Console logging
        logger.add(
            sys.stderr,
            level=self.config.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>TG-Bot</cyan> | {message}",
            colorize=True
        )
        
        # File logging
        if self.config.log_file:
            logger.add(
                self.config.log_file,
                level=self.config.log_level,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | TG-Bot | {message}",
                rotation="1 day",
                retention="30 days"
            )
    
    async def initialize(self) -> bool:
        """Initialize all bot components."""
        try:
            logger.info("🚀 Initializing Telegram Bot components...")
            
            # Initialize MCP Manager
            self.mcp_manager = MCPManager(
                memory_server_url=self.config.memory_server_url
            )
            mcp_success = await self.mcp_manager.initialize()
            
            if mcp_success:
                logger.success("✅ MCP Manager initialized")
                
                # Save bot initialization to memory
                await self.mcp_manager.save_project_state({
                    "component": "telegram_bot",
                    "status": "initializing",
                    "timestamp": datetime.now().isoformat(),
                    "config": {
                        "use_webhook": self.config.use_webhook,
                        "krabulon_enabled": self.config.krabulon_enabled,
                        "admin_users": len(self.config.admin_user_ids)
                    }
                })
            else:
                logger.warning("⚠️ MCP Manager initialization failed - continuing without memory")
            
            # Initialize Krabulon integration
            if self.config.krabulon_enabled:
                self.krabulon_integration = KrabulonIntegration(
                    base_path=self.config.krabulon_base_path,
                    mcp_manager=self.mcp_manager
                )
                krabulon_success = await self.krabulon_integration.initialize()

                if krabulon_success:
                    logger.success("✅ Krabulon integration initialized")
                else:
                    logger.warning("⚠️ Krabulon integration failed - continuing without deep crawling")

            # Initialize AI Engine
            self.ai_engine = AdvancedAIEngine()
            ai_success = await self.ai_engine.initialize()

            if ai_success:
                logger.success("✅ AI Engine initialized with LM Studio")
            else:
                logger.warning("⚠️ AI Engine in fallback mode - LM Studio unavailable")

            # Initialize Real-time Integrations
            self.realtime_integrations = RealTimeIntegrations(self)
            await self.realtime_integrations.start()
            logger.success("✅ Real-time integrations started")
            
            # Setup middleware and handlers
            setup_middleware(self.dp, self.config)
            setup_handlers(self.router, self)
            self.dp.include_router(self.router)
            
            # Test bot connection
            bot_info = await self.bot.get_me()
            logger.success(f"✅ Bot connected: @{bot_info.username} ({bot_info.first_name})")
            
            self.start_time = datetime.now()
            logger.success("🎉 Telegram Bot fully initialized!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Bot initialization failed: {e}")
            return False
    
    async def start_polling(self):
        """Start bot in polling mode."""
        if not await self.initialize():
            raise RuntimeError("Bot initialization failed")
        
        self.is_running = True
        logger.info("🔄 Starting bot in polling mode...")
        
        try:
            # Update MCP with running status
            if self.mcp_manager:
                await self.mcp_manager.save_project_state({
                    "component": "telegram_bot",
                    "status": "running",
                    "mode": "polling",
                    "start_time": self.start_time.isoformat()
                })
            
            await self.dp.start_polling(self.bot)
            
        except Exception as e:
            logger.error(f"❌ Polling error: {e}")
            self.error_count += 1
            raise
        finally:
            self.is_running = False
            await self.cleanup()
    
    async def start_webhook(self):
        """Start bot in webhook mode."""
        if not await self.initialize():
            raise RuntimeError("Bot initialization failed")
        
        if not self.config.webhook_url:
            raise ValueError("Webhook URL not configured")
        
        self.is_running = True
        logger.info(f"🌐 Starting bot in webhook mode on {self.config.host}:{self.config.port}")
        
        try:
            # Set webhook
            await self.bot.set_webhook(
                url=self.config.webhook_url,
                secret_token=self.config.webhook_secret
            )
            
            # Create aiohttp application
            app = web.Application()
            
            # Setup webhook handler
            webhook_requests_handler = SimpleRequestHandler(
                dispatcher=self.dp,
                bot=self.bot,
                secret_token=self.config.webhook_secret
            )
            webhook_requests_handler.register(app, path="/webhook")
            
            # Setup application
            setup_application(app, self.dp, bot=self.bot)
            
            # Update MCP with running status
            if self.mcp_manager:
                await self.mcp_manager.save_project_state({
                    "component": "telegram_bot",
                    "status": "running",
                    "mode": "webhook",
                    "webhook_url": self.config.webhook_url,
                    "start_time": self.start_time.isoformat()
                })
            
            # Start web server
            runner = web.AppRunner(app)
            await runner.setup()
            
            site = web.TCPSite(runner, self.config.host, self.config.port)
            await site.start()
            
            logger.success(f"🎉 Webhook server started on {self.config.host}:{self.config.port}")
            
            # Keep running
            while self.is_running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"❌ Webhook error: {e}")
            self.error_count += 1
            raise
        finally:
            await self.cleanup()
    
    async def get_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status."""
        uptime = None
        if self.start_time:
            uptime = str(datetime.now() - self.start_time)
        
        status = {
            "bot_status": {
                "is_running": self.is_running,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "uptime": uptime,
                "message_count": self.message_count,
                "error_count": self.error_count,
                "mode": "webhook" if self.config.use_webhook else "polling"
            },
            "integrations": {
                "mcp_connected": self.mcp_manager is not None and self.mcp_manager.status.memory_connected,
                "krabulon_enabled": self.config.krabulon_enabled,
                "krabulon_ready": self.krabulon_integration is not None and self.krabulon_integration.is_ready
            }
        }
        
        # Add MCP status if available
        if self.mcp_manager:
            mcp_status = await self.mcp_manager.get_comprehensive_status()
            status["mcp_status"] = mcp_status
        
        # Add Krabulon status if available
        if self.krabulon_integration:
            krabulon_status = await self.krabulon_integration.get_status()
            status["krabulon_status"] = krabulon_status
        
        return status
    
    async def cleanup(self):
        """Cleanup bot resources."""
        logger.info("🔄 Cleaning up bot resources...")
        
        try:
            # Update MCP with shutdown status
            if self.mcp_manager:
                await self.mcp_manager.save_project_state({
                    "component": "telegram_bot",
                    "status": "stopped",
                    "stop_time": datetime.now().isoformat(),
                    "final_stats": {
                        "message_count": self.message_count,
                        "error_count": self.error_count,
                        "uptime": str(datetime.now() - self.start_time) if self.start_time else None
                    }
                })
                
                await self.mcp_manager.close()
            
            # Cleanup Krabulon integration
            if self.krabulon_integration:
                await self.krabulon_integration.cleanup()

            # Cleanup AI Engine
            if self.ai_engine:
                await self.ai_engine.close()

            # Cleanup Real-time Integrations
            if self.realtime_integrations:
                await self.realtime_integrations.stop()

            # Close bot session
            await self.bot.session.close()
            
            logger.success("✅ Bot cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")
    
    def increment_message_count(self):
        """Increment message counter."""
        self.message_count += 1
    
    def increment_error_count(self):
        """Increment error counter."""
        self.error_count += 1
