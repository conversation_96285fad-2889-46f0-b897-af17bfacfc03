"""
Advanced Analytics Engine for Telegram Bot
Real-time analytics, user behavior tracking, and business intelligence
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import statistics

from loguru import logger


@dataclass
class UserAnalytics:
    """User analytics data structure."""
    user_id: int
    username: Optional[str]
    first_seen: datetime
    last_seen: datetime
    total_messages: int
    commands_used: Dict[str, int]
    files_uploaded: int
    ai_queries: int
    search_queries: int
    favorite_manufacturers: List[str]
    engagement_score: float
    user_type: str  # new, regular, power_user, admin


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    active_users_1h: int
    active_users_24h: int
    total_messages_1h: int
    avg_response_time: float
    error_rate: float
    ai_usage_rate: float
    search_success_rate: float
    file_processing_rate: float
    system_load: float


@dataclass
class BusinessInsight:
    """Business intelligence insight."""
    insight_id: str
    category: str
    title: str
    description: str
    impact_level: str  # low, medium, high, critical
    confidence: float
    data_points: List[Dict[str, Any]]
    recommendations: List[str]
    generated_at: datetime


class AdvancedAnalyticsEngine:
    """
    Advanced Analytics Engine for HVAC CRM Telegram Bot.
    
    Features:
    - Real-time user behavior tracking
    - Performance metrics collection
    - Business intelligence insights
    - Predictive analytics
    - Custom dashboard generation
    - Automated reporting
    """
    
    def __init__(self, bot_manager):
        self.bot_manager = bot_manager
        
        # Analytics storage
        self.user_analytics: Dict[int, UserAnalytics] = {}
        self.system_metrics: List[SystemMetrics] = []
        self.business_insights: List[BusinessInsight] = []
        
        # Tracking data
        self.command_usage = Counter()
        self.search_queries = []
        self.ai_interactions = []
        self.file_uploads = []
        self.error_events = []
        
        # Performance tracking
        self.response_times = []
        self.active_sessions = set()
        self.hourly_stats = defaultdict(lambda: defaultdict(int))
        
        # Business metrics
        self.conversion_funnel = {
            "visitors": 0,
            "engaged_users": 0,
            "search_users": 0,
            "ai_users": 0,
            "quote_requests": 0
        }
        
        self.is_running = False
        self.analytics_tasks: List[asyncio.Task] = []
        
        logger.info("📊 Advanced Analytics Engine initialized")
    
    async def start(self):
        """Start analytics engine."""
        try:
            logger.info("🚀 Starting analytics engine...")
            
            self.is_running = True
            
            # Start analytics tasks
            tasks = [
                self._collect_metrics(),
                self._analyze_user_behavior(),
                self._generate_insights(),
                self._cleanup_old_data()
            ]
            
            for task_func in tasks:
                task = asyncio.create_task(task_func)
                self.analytics_tasks.append(task)
            
            logger.success("✅ Analytics engine started")
            
        except Exception as e:
            logger.error(f"Failed to start analytics engine: {e}")
    
    async def stop(self):
        """Stop analytics engine."""
        try:
            logger.info("🔄 Stopping analytics engine...")
            
            self.is_running = False
            
            # Cancel all tasks
            for task in self.analytics_tasks:
                task.cancel()
            
            await asyncio.gather(*self.analytics_tasks, return_exceptions=True)
            self.analytics_tasks.clear()
            
            logger.success("✅ Analytics engine stopped")
            
        except Exception as e:
            logger.error(f"Error stopping analytics engine: {e}")
    
    async def track_user_interaction(
        self,
        user_id: int,
        username: Optional[str],
        interaction_type: str,
        data: Dict[str, Any] = None
    ):
        """Track user interaction."""
        try:
            current_time = datetime.now()
            
            # Update or create user analytics
            if user_id not in self.user_analytics:
                self.user_analytics[user_id] = UserAnalytics(
                    user_id=user_id,
                    username=username,
                    first_seen=current_time,
                    last_seen=current_time,
                    total_messages=0,
                    commands_used={},
                    files_uploaded=0,
                    ai_queries=0,
                    search_queries=0,
                    favorite_manufacturers=[],
                    engagement_score=0.0,
                    user_type="new"
                )
            
            user_analytics = self.user_analytics[user_id]
            user_analytics.last_seen = current_time
            user_analytics.total_messages += 1
            
            # Track specific interactions
            if interaction_type == "command":
                command = data.get("command", "unknown")
                user_analytics.commands_used[command] = user_analytics.commands_used.get(command, 0) + 1
                self.command_usage[command] += 1
            
            elif interaction_type == "search":
                user_analytics.search_queries += 1
                self.search_queries.append({
                    "user_id": user_id,
                    "query": data.get("query", ""),
                    "timestamp": current_time,
                    "results_count": data.get("results_count", 0)
                })
            
            elif interaction_type == "ai_query":
                user_analytics.ai_queries += 1
                self.ai_interactions.append({
                    "user_id": user_id,
                    "query_type": data.get("query_type", ""),
                    "timestamp": current_time,
                    "confidence": data.get("confidence", 0.0)
                })
            
            elif interaction_type == "file_upload":
                user_analytics.files_uploaded += 1
                self.file_uploads.append({
                    "user_id": user_id,
                    "file_type": data.get("file_type", ""),
                    "file_size": data.get("file_size", 0),
                    "timestamp": current_time
                })
            
            # Update engagement score
            user_analytics.engagement_score = self._calculate_engagement_score(user_analytics)
            
            # Update user type
            user_analytics.user_type = self._determine_user_type(user_analytics)
            
            # Track active session
            self.active_sessions.add(user_id)
            
            # Update conversion funnel
            self._update_conversion_funnel(user_analytics, interaction_type)
            
        except Exception as e:
            logger.error(f"User interaction tracking failed: {e}")
    
    async def track_performance_metric(
        self,
        metric_type: str,
        value: float,
        metadata: Dict[str, Any] = None
    ):
        """Track performance metric."""
        try:
            if metric_type == "response_time":
                self.response_times.append({
                    "value": value,
                    "timestamp": datetime.now(),
                    "metadata": metadata or {}
                })
                
                # Keep only recent response times
                cutoff_time = datetime.now() - timedelta(hours=1)
                self.response_times = [
                    rt for rt in self.response_times 
                    if rt["timestamp"] > cutoff_time
                ]
            
            elif metric_type == "error":
                self.error_events.append({
                    "error_type": metadata.get("error_type", "unknown"),
                    "component": metadata.get("component", "unknown"),
                    "timestamp": datetime.now(),
                    "severity": metadata.get("severity", "low")
                })
            
        except Exception as e:
            logger.error(f"Performance metric tracking failed: {e}")
    
    async def get_analytics_dashboard(self) -> Dict[str, Any]:
        """Generate comprehensive analytics dashboard."""
        try:
            current_time = datetime.now()
            
            # Calculate time ranges
            last_hour = current_time - timedelta(hours=1)
            last_24h = current_time - timedelta(hours=24)
            last_7d = current_time - timedelta(days=7)
            
            # User metrics
            total_users = len(self.user_analytics)
            active_users_1h = len([
                u for u in self.user_analytics.values() 
                if u.last_seen > last_hour
            ])
            active_users_24h = len([
                u for u in self.user_analytics.values() 
                if u.last_seen > last_24h
            ])
            
            # Performance metrics
            avg_response_time = 0.0
            if self.response_times:
                recent_times = [
                    rt["value"] for rt in self.response_times 
                    if rt["timestamp"] > last_hour
                ]
                if recent_times:
                    avg_response_time = statistics.mean(recent_times)
            
            # Error rate
            recent_errors = [
                e for e in self.error_events 
                if e["timestamp"] > last_hour
            ]
            error_rate = len(recent_errors) / max(active_users_1h, 1) * 100
            
            # Command usage
            top_commands = dict(self.command_usage.most_common(10))
            
            # Search analytics
            recent_searches = [
                s for s in self.search_queries 
                if s["timestamp"] > last_24h
            ]
            search_success_rate = 0.0
            if recent_searches:
                successful_searches = [s for s in recent_searches if s["results_count"] > 0]
                search_success_rate = len(successful_searches) / len(recent_searches) * 100
            
            # AI usage
            recent_ai = [
                ai for ai in self.ai_interactions 
                if ai["timestamp"] > last_24h
            ]
            ai_usage_rate = len(recent_ai) / max(active_users_24h, 1) * 100
            
            # User engagement
            engagement_scores = [u.engagement_score for u in self.user_analytics.values()]
            avg_engagement = statistics.mean(engagement_scores) if engagement_scores else 0.0
            
            # Business metrics
            conversion_rates = {
                "visitor_to_engaged": (self.conversion_funnel["engaged_users"] / max(self.conversion_funnel["visitors"], 1)) * 100,
                "engaged_to_search": (self.conversion_funnel["search_users"] / max(self.conversion_funnel["engaged_users"], 1)) * 100,
                "search_to_ai": (self.conversion_funnel["ai_users"] / max(self.conversion_funnel["search_users"], 1)) * 100,
                "ai_to_quote": (self.conversion_funnel["quote_requests"] / max(self.conversion_funnel["ai_users"], 1)) * 100
            }
            
            return {
                "timestamp": current_time.isoformat(),
                "user_metrics": {
                    "total_users": total_users,
                    "active_users_1h": active_users_1h,
                    "active_users_24h": active_users_24h,
                    "new_users_24h": len([u for u in self.user_analytics.values() if u.first_seen > last_24h]),
                    "avg_engagement_score": avg_engagement
                },
                "performance_metrics": {
                    "avg_response_time": avg_response_time,
                    "error_rate": error_rate,
                    "search_success_rate": search_success_rate,
                    "ai_usage_rate": ai_usage_rate
                },
                "usage_analytics": {
                    "top_commands": top_commands,
                    "total_searches_24h": len(recent_searches),
                    "total_ai_queries_24h": len(recent_ai),
                    "total_file_uploads_24h": len([f for f in self.file_uploads if f["timestamp"] > last_24h])
                },
                "business_metrics": {
                    "conversion_funnel": self.conversion_funnel,
                    "conversion_rates": conversion_rates
                },
                "insights": [asdict(insight) for insight in self.business_insights[-5:]]
            }
            
        except Exception as e:
            logger.error(f"Analytics dashboard generation failed: {e}")
            return {"error": str(e)}
    
    async def generate_user_report(self, user_id: int) -> Dict[str, Any]:
        """Generate detailed user report."""
        try:
            if user_id not in self.user_analytics:
                return {"error": "User not found"}
            
            user = self.user_analytics[user_id]
            
            # Calculate user-specific metrics
            user_searches = [s for s in self.search_queries if s["user_id"] == user_id]
            user_ai_queries = [ai for ai in self.ai_interactions if ai["user_id"] == user_id]
            user_files = [f for f in self.file_uploads if f["user_id"] == user_id]
            
            # Activity timeline
            activity_timeline = []
            for search in user_searches[-10:]:
                activity_timeline.append({
                    "type": "search",
                    "timestamp": search["timestamp"],
                    "details": search["query"]
                })
            
            for ai_query in user_ai_queries[-10:]:
                activity_timeline.append({
                    "type": "ai_query",
                    "timestamp": ai_query["timestamp"],
                    "details": ai_query["query_type"]
                })
            
            # Sort by timestamp
            activity_timeline.sort(key=lambda x: x["timestamp"], reverse=True)
            
            return {
                "user_profile": asdict(user),
                "activity_summary": {
                    "total_searches": len(user_searches),
                    "total_ai_queries": len(user_ai_queries),
                    "total_file_uploads": len(user_files),
                    "days_active": (user.last_seen - user.first_seen).days,
                    "avg_daily_messages": user.total_messages / max((user.last_seen - user.first_seen).days, 1)
                },
                "recent_activity": activity_timeline[:20],
                "preferences": {
                    "most_used_commands": dict(Counter(user.commands_used).most_common(5)),
                    "favorite_manufacturers": user.favorite_manufacturers
                }
            }
            
        except Exception as e:
            logger.error(f"User report generation failed: {e}")
            return {"error": str(e)}
    
    def _calculate_engagement_score(self, user: UserAnalytics) -> float:
        """Calculate user engagement score."""
        try:
            score = 0.0
            
            # Base activity score
            score += min(user.total_messages / 100, 1.0) * 30
            
            # Command diversity
            score += min(len(user.commands_used) / 10, 1.0) * 20
            
            # Advanced feature usage
            if user.ai_queries > 0:
                score += 25
            if user.search_queries > 0:
                score += 15
            if user.files_uploaded > 0:
                score += 10
            
            return min(score, 100.0)
            
        except Exception:
            return 0.0
    
    def _determine_user_type(self, user: UserAnalytics) -> str:
        """Determine user type based on activity."""
        try:
            if user.total_messages < 5:
                return "new"
            elif user.engagement_score > 80:
                return "power_user"
            elif user.engagement_score > 40:
                return "regular"
            else:
                return "casual"
                
        except Exception:
            return "unknown"
    
    def _update_conversion_funnel(self, user: UserAnalytics, interaction_type: str):
        """Update conversion funnel metrics."""
        try:
            # All users are visitors
            self.conversion_funnel["visitors"] = len(self.user_analytics)
            
            # Engaged users (more than 3 messages)
            engaged_users = [u for u in self.user_analytics.values() if u.total_messages > 3]
            self.conversion_funnel["engaged_users"] = len(engaged_users)
            
            # Search users
            search_users = [u for u in self.user_analytics.values() if u.search_queries > 0]
            self.conversion_funnel["search_users"] = len(search_users)
            
            # AI users
            ai_users = [u for u in self.user_analytics.values() if u.ai_queries > 0]
            self.conversion_funnel["ai_users"] = len(ai_users)
            
            # Quote requests (users who used quote command)
            quote_users = [
                u for u in self.user_analytics.values() 
                if "quote" in u.commands_used
            ]
            self.conversion_funnel["quote_requests"] = len(quote_users)
            
        except Exception as e:
            logger.error(f"Conversion funnel update failed: {e}")
    
    async def _collect_metrics(self):
        """Collect system metrics periodically."""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Calculate metrics
                metrics = SystemMetrics(
                    timestamp=current_time,
                    active_users_1h=len([
                        u for u in self.user_analytics.values() 
                        if u.last_seen > current_time - timedelta(hours=1)
                    ]),
                    active_users_24h=len([
                        u for u in self.user_analytics.values() 
                        if u.last_seen > current_time - timedelta(hours=24)
                    ]),
                    total_messages_1h=sum([
                        1 for u in self.user_analytics.values() 
                        if u.last_seen > current_time - timedelta(hours=1)
                    ]),
                    avg_response_time=statistics.mean([
                        rt["value"] for rt in self.response_times[-100:]
                    ]) if self.response_times else 0.0,
                    error_rate=len([
                        e for e in self.error_events 
                        if e["timestamp"] > current_time - timedelta(hours=1)
                    ]) / 100,
                    ai_usage_rate=len([
                        ai for ai in self.ai_interactions 
                        if ai["timestamp"] > current_time - timedelta(hours=1)
                    ]) / 100,
                    search_success_rate=80.0,  # Placeholder
                    file_processing_rate=95.0,  # Placeholder
                    system_load=50.0  # Placeholder
                )
                
                self.system_metrics.append(metrics)
                
                # Keep only recent metrics
                cutoff_time = current_time - timedelta(days=7)
                self.system_metrics = [
                    m for m in self.system_metrics 
                    if m.timestamp > cutoff_time
                ]
                
                await asyncio.sleep(300)  # Collect every 5 minutes
                
            except Exception as e:
                logger.error(f"Metrics collection error: {e}")
                await asyncio.sleep(600)
    
    async def _analyze_user_behavior(self):
        """Analyze user behavior patterns."""
        while self.is_running:
            try:
                # Analyze user patterns every hour
                await asyncio.sleep(3600)
                
                # This would contain more sophisticated behavior analysis
                logger.info("📊 User behavior analysis completed")
                
            except Exception as e:
                logger.error(f"User behavior analysis error: {e}")
                await asyncio.sleep(3600)
    
    async def _generate_insights(self):
        """Generate business insights."""
        while self.is_running:
            try:
                # Generate insights every 6 hours
                await asyncio.sleep(21600)
                
                # This would contain AI-powered insight generation
                logger.info("💡 Business insights generated")
                
            except Exception as e:
                logger.error(f"Insight generation error: {e}")
                await asyncio.sleep(21600)
    
    async def _cleanup_old_data(self):
        """Clean up old analytics data."""
        while self.is_running:
            try:
                # Cleanup every 24 hours
                await asyncio.sleep(86400)
                
                current_time = datetime.now()
                cutoff_time = current_time - timedelta(days=30)
                
                # Clean up old data
                self.search_queries = [
                    s for s in self.search_queries 
                    if s["timestamp"] > cutoff_time
                ]
                
                self.ai_interactions = [
                    ai for ai in self.ai_interactions 
                    if ai["timestamp"] > cutoff_time
                ]
                
                self.file_uploads = [
                    f for f in self.file_uploads 
                    if f["timestamp"] > cutoff_time
                ]
                
                self.error_events = [
                    e for e in self.error_events 
                    if e["timestamp"] > cutoff_time
                ]
                
                logger.info("🧹 Analytics data cleanup completed")
                
            except Exception as e:
                logger.error(f"Data cleanup error: {e}")
                await asyncio.sleep(86400)
