"""
Advanced AI Engine for Telegram Bot
Integrates LM Studio, Gemma3-4b, and advanced AI capabilities
"""

import asyncio
import json
import aiohttp
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass

from loguru import logger


@dataclass
class AIResponse:
    """AI response structure."""
    content: str
    confidence: float
    reasoning: str
    suggestions: List[str]
    metadata: Dict[str, Any]
    processing_time: float


@dataclass
class HVACAnalysis:
    """HVAC equipment analysis result."""
    equipment_type: str
    manufacturer: str
    model: str
    capacity: str
    efficiency_rating: str
    condition_assessment: str
    maintenance_recommendations: List[str]
    estimated_value: str
    replacement_suggestions: List[str]
    confidence_score: float


class AdvancedAIEngine:
    """
    Advanced AI Engine for HVAC CRM Telegram Bot.
    
    Capabilities:
    - Natural language understanding
    - Equipment identification and analysis
    - Predictive maintenance recommendations
    - Quote generation and pricing
    - Customer sentiment analysis
    - Technical documentation processing
    """
    
    def __init__(self, lm_studio_url: str = "http://*************:1234"):
        self.lm_studio_url = lm_studio_url
        self.model_name = "gemma3-4b"
        self.session: Optional[aiohttp.ClientSession] = None
        
        # AI prompts and templates
        self.prompts = {
            "equipment_analysis": """
You are an expert HVAC technician and equipment analyst. Analyze the following equipment information and provide detailed insights.

Equipment Data: {equipment_data}
Context: {context}

Provide analysis in this JSON format:
{{
    "equipment_type": "type of HVAC equipment",
    "manufacturer": "manufacturer name",
    "model": "model number",
    "capacity": "cooling/heating capacity",
    "efficiency_rating": "energy efficiency rating",
    "condition_assessment": "current condition assessment",
    "maintenance_recommendations": ["recommendation1", "recommendation2"],
    "estimated_value": "estimated market value",
    "replacement_suggestions": ["suggestion1", "suggestion2"],
    "confidence_score": 0.95
}}
            """,
            
            "customer_query": """
You are a helpful HVAC expert assistant. Answer the customer's question with technical accuracy and friendly tone.

Customer Question: {query}
Available Data: {context}
Previous Conversation: {history}

Provide a comprehensive answer that includes:
1. Direct answer to the question
2. Technical explanation if needed
3. Practical recommendations
4. Next steps or follow-up suggestions

Keep the response conversational but informative.
            """,
            
            "maintenance_prediction": """
You are a predictive maintenance expert for HVAC systems. Analyze the equipment data and predict maintenance needs.

Equipment Information: {equipment_info}
Usage History: {usage_data}
Environmental Factors: {environment}

Predict:
1. Next maintenance date
2. Potential issues to watch for
3. Preventive actions
4. Cost estimates
5. Priority level (1-5)
            """,
            
            "quote_generation": """
You are an HVAC sales and pricing expert. Generate a professional quote based on the requirements.

Customer Requirements: {requirements}
Equipment Specifications: {equipment_specs}
Installation Complexity: {complexity}
Market Conditions: {market_data}

Generate a detailed quote including:
1. Equipment costs
2. Installation costs
3. Additional materials
4. Labor estimates
5. Timeline
6. Warranty information
7. Total investment
            """
        }
        
        logger.info("🧠 Advanced AI Engine initialized")
    
    async def initialize(self) -> bool:
        """Initialize AI engine and test connection."""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test LM Studio connection
            health_check = await self._test_lm_studio_connection()
            
            if health_check:
                logger.success("✅ AI Engine connected to LM Studio")
                return True
            else:
                logger.warning("⚠️ LM Studio connection failed - using fallback mode")
                return False
                
        except Exception as e:
            logger.error(f"AI Engine initialization failed: {e}")
            return False
    
    async def analyze_equipment(
        self,
        equipment_data: Dict[str, Any],
        context: str = "",
        image_data: Optional[bytes] = None
    ) -> HVACAnalysis:
        """Analyze HVAC equipment using AI."""
        try:
            logger.info("🔍 Starting AI equipment analysis")
            
            # Prepare prompt
            prompt = self.prompts["equipment_analysis"].format(
                equipment_data=json.dumps(equipment_data, indent=2),
                context=context
            )
            
            # Get AI response
            ai_response = await self._query_lm_studio(prompt)
            
            if ai_response:
                # Parse AI response
                try:
                    analysis_data = json.loads(ai_response.content)
                    
                    return HVACAnalysis(
                        equipment_type=analysis_data.get("equipment_type", "Unknown"),
                        manufacturer=analysis_data.get("manufacturer", "Unknown"),
                        model=analysis_data.get("model", "Unknown"),
                        capacity=analysis_data.get("capacity", "Unknown"),
                        efficiency_rating=analysis_data.get("efficiency_rating", "Unknown"),
                        condition_assessment=analysis_data.get("condition_assessment", "Unknown"),
                        maintenance_recommendations=analysis_data.get("maintenance_recommendations", []),
                        estimated_value=analysis_data.get("estimated_value", "Unknown"),
                        replacement_suggestions=analysis_data.get("replacement_suggestions", []),
                        confidence_score=analysis_data.get("confidence_score", 0.0)
                    )
                    
                except json.JSONDecodeError:
                    # Fallback to text analysis
                    return await self._fallback_equipment_analysis(equipment_data)
            
            else:
                return await self._fallback_equipment_analysis(equipment_data)
                
        except Exception as e:
            logger.error(f"Equipment analysis failed: {e}")
            return await self._fallback_equipment_analysis(equipment_data)
    
    async def process_customer_query(
        self,
        query: str,
        context: Dict[str, Any] = None,
        conversation_history: List[str] = None
    ) -> AIResponse:
        """Process customer query with AI understanding."""
        try:
            logger.info(f"💬 Processing customer query: {query[:50]}...")
            
            # Prepare context
            context_str = json.dumps(context or {}, indent=2)
            history_str = "\n".join(conversation_history or [])
            
            prompt = self.prompts["customer_query"].format(
                query=query,
                context=context_str,
                history=history_str
            )
            
            # Get AI response
            ai_response = await self._query_lm_studio(prompt)
            
            if ai_response:
                return ai_response
            else:
                return await self._fallback_customer_response(query)
                
        except Exception as e:
            logger.error(f"Customer query processing failed: {e}")
            return await self._fallback_customer_response(query)
    
    async def predict_maintenance(
        self,
        equipment_info: Dict[str, Any],
        usage_data: Dict[str, Any] = None,
        environment: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Predict maintenance needs using AI."""
        try:
            logger.info("🔮 Generating maintenance predictions")
            
            prompt = self.prompts["maintenance_prediction"].format(
                equipment_info=json.dumps(equipment_info, indent=2),
                usage_data=json.dumps(usage_data or {}, indent=2),
                environment=json.dumps(environment or {}, indent=2)
            )
            
            ai_response = await self._query_lm_studio(prompt)
            
            if ai_response:
                return {
                    "prediction": ai_response.content,
                    "confidence": ai_response.confidence,
                    "generated_at": datetime.now().isoformat(),
                    "model_used": self.model_name
                }
            else:
                return await self._fallback_maintenance_prediction(equipment_info)
                
        except Exception as e:
            logger.error(f"Maintenance prediction failed: {e}")
            return await self._fallback_maintenance_prediction(equipment_info)
    
    async def generate_quote(
        self,
        requirements: Dict[str, Any],
        equipment_specs: Dict[str, Any] = None,
        complexity: str = "medium"
    ) -> Dict[str, Any]:
        """Generate professional quote using AI."""
        try:
            logger.info("💰 Generating AI-powered quote")
            
            # Get market data (placeholder)
            market_data = {
                "current_date": datetime.now().isoformat(),
                "market_conditions": "stable",
                "material_costs": "moderate",
                "labor_availability": "good"
            }
            
            prompt = self.prompts["quote_generation"].format(
                requirements=json.dumps(requirements, indent=2),
                equipment_specs=json.dumps(equipment_specs or {}, indent=2),
                complexity=complexity,
                market_data=json.dumps(market_data, indent=2)
            )
            
            ai_response = await self._query_lm_studio(prompt)
            
            if ai_response:
                return {
                    "quote_content": ai_response.content,
                    "confidence": ai_response.confidence,
                    "generated_at": datetime.now().isoformat(),
                    "valid_until": "30 days",
                    "quote_id": f"Q{datetime.now().strftime('%Y%m%d%H%M%S')}"
                }
            else:
                return await self._fallback_quote_generation(requirements)
                
        except Exception as e:
            logger.error(f"Quote generation failed: {e}")
            return await self._fallback_quote_generation(requirements)
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze customer sentiment."""
        try:
            # Simple sentiment analysis (can be enhanced with specialized models)
            positive_words = ["good", "great", "excellent", "satisfied", "happy", "pleased"]
            negative_words = ["bad", "terrible", "awful", "disappointed", "angry", "frustrated"]
            
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count:
                sentiment = "positive"
                score = 0.7 + (positive_count * 0.1)
            elif negative_count > positive_count:
                sentiment = "negative"
                score = 0.3 - (negative_count * 0.1)
            else:
                sentiment = "neutral"
                score = 0.5
            
            return {
                "sentiment": sentiment,
                "score": max(0.0, min(1.0, score)),
                "confidence": 0.8,
                "analysis": f"Detected {positive_count} positive and {negative_count} negative indicators"
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return {"sentiment": "neutral", "score": 0.5, "confidence": 0.0}
    
    async def _query_lm_studio(self, prompt: str) -> Optional[AIResponse]:
        """Query LM Studio API."""
        try:
            if not self.session:
                return None
            
            start_time = datetime.now()
            
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 2000,
                "stream": False
            }
            
            async with self.session.post(
                f"{self.lm_studio_url}/v1/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    content = data["choices"][0]["message"]["content"]
                    
                    processing_time = (datetime.now() - start_time).total_seconds()
                    
                    return AIResponse(
                        content=content,
                        confidence=0.9,  # LM Studio responses are generally reliable
                        reasoning="Generated by Gemma3-4b via LM Studio",
                        suggestions=[],
                        metadata={
                            "model": self.model_name,
                            "tokens_used": data.get("usage", {}).get("total_tokens", 0),
                            "response_time": processing_time
                        },
                        processing_time=processing_time
                    )
                else:
                    logger.warning(f"LM Studio API error: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"LM Studio query failed: {e}")
            return None
    
    async def _test_lm_studio_connection(self) -> bool:
        """Test LM Studio connection."""
        try:
            if not self.session:
                return False
            
            async with self.session.get(
                f"{self.lm_studio_url}/v1/models",
                timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                return response.status == 200
                
        except Exception:
            return False
    
    async def _fallback_equipment_analysis(self, equipment_data: Dict[str, Any]) -> HVACAnalysis:
        """Fallback equipment analysis without AI."""
        return HVACAnalysis(
            equipment_type=equipment_data.get("type", "Unknown"),
            manufacturer=equipment_data.get("manufacturer", "Unknown"),
            model=equipment_data.get("model", "Unknown"),
            capacity=equipment_data.get("capacity", "Unknown"),
            efficiency_rating="Unknown",
            condition_assessment="Requires inspection",
            maintenance_recommendations=["Schedule professional inspection"],
            estimated_value="Contact for pricing",
            replacement_suggestions=["Consult with HVAC specialist"],
            confidence_score=0.5
        )
    
    async def _fallback_customer_response(self, query: str) -> AIResponse:
        """Fallback customer response without AI."""
        return AIResponse(
            content=f"Thank you for your question about '{query[:50]}...'. I'll help you find the information you need. Please use our search function or contact our support team for detailed assistance.",
            confidence=0.6,
            reasoning="Fallback response - AI unavailable",
            suggestions=["Try /search command", "Contact support", "Upload equipment photos"],
            metadata={"fallback": True},
            processing_time=0.1
        )
    
    async def _fallback_maintenance_prediction(self, equipment_info: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback maintenance prediction."""
        return {
            "prediction": "Regular maintenance recommended every 6 months. Schedule professional inspection to assess current condition and maintenance needs.",
            "confidence": 0.5,
            "generated_at": datetime.now().isoformat(),
            "model_used": "fallback"
        }
    
    async def _fallback_quote_generation(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback quote generation."""
        return {
            "quote_content": "Thank you for your interest. A detailed quote will be prepared by our sales team based on your specific requirements. Please contact us for a personalized consultation.",
            "confidence": 0.5,
            "generated_at": datetime.now().isoformat(),
            "valid_until": "Contact for details",
            "quote_id": f"Q{datetime.now().strftime('%Y%m%d%H%M%S')}"
        }
    
    async def close(self):
        """Close AI engine resources."""
        if self.session:
            await self.session.close()
        logger.info("🧠 AI Engine closed")
