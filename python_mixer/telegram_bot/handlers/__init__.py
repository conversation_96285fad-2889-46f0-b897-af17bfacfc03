"""
Telegram Bot Handlers
"""

from .basic_handlers import setup_basic_handlers
from .hvac_handlers import setup_hvac_handlers
from .admin_handlers import setup_admin_handlers
from .file_handlers import setup_file_handlers
from .ai_handlers import setup_ai_handlers


def setup_handlers(router, bot_manager):
    """Setup all bot handlers."""

    # Setup different handler groups
    setup_basic_handlers(router, bot_manager)
    setup_hvac_handlers(router, bot_manager)
    setup_admin_handlers(router, bot_manager)
    setup_file_handlers(router, bot_manager)
    setup_ai_handlers(router, bot_manager)
    
    from loguru import logger
    logger.info("🎯 All bot handlers setup completed")
