"""
AI-powered Telegram Bot Handlers
Advanced AI capabilities using LM Studio and Gemma3-4b
"""

import asyncio
import json
from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder
from loguru import logger


def setup_ai_handlers(router: Router, bot_manager):
    """Setup AI-powered command handlers."""
    
    @router.message(Command("analyze"))
    async def ai_analyze_handler(message: Message):
        """Handle /analyze command for AI-powered equipment analysis."""
        query = message.text.replace("/analyze", "").strip()
        
        if not query:
            await message.answer(
                "🧠 <b>AI Equipment Analysis</b>\n\n"
                "Provide equipment information for AI analysis.\n\n"
                "<b>Usage:</b> /analyze [equipment description]\n\n"
                "<b>Examples:</b>\n"
                "• /analyze LG S12ET split unit, 5 years old, making noise\n"
                "• /analyze Daikin heat pump, high energy bills\n"
                "• /analyze Commercial HVAC system, poor cooling",
                parse_mode="HTML"
            )
            return
        
        # Show analysis message
        analyzing_msg = await message.answer(
            f"🧠 <b>AI Analysis in Progress...</b>\n\n"
            f"Query: {query}\n\n"
            "⏳ Gemma3-4b is analyzing your equipment...\n"
            "This may take a few moments.",
            parse_mode="HTML"
        )
        
        try:
            if bot_manager.ai_engine:
                # Prepare equipment data from query
                equipment_data = {
                    "description": query,
                    "source": "user_input",
                    "timestamp": message.date.isoformat()
                }
                
                # Get AI analysis
                analysis = await bot_manager.ai_engine.analyze_equipment(
                    equipment_data=equipment_data,
                    context=f"User query from Telegram user {message.from_user.id}"
                )
                
                # Format analysis response
                response_text = f"🧠 <b>AI Equipment Analysis</b>\n\n"
                response_text += f"<b>🔍 Equipment Identified:</b>\n"
                response_text += f"• Type: {analysis.equipment_type}\n"
                response_text += f"• Manufacturer: {analysis.manufacturer}\n"
                response_text += f"• Model: {analysis.model}\n"
                response_text += f"• Capacity: {analysis.capacity}\n"
                response_text += f"• Efficiency: {analysis.efficiency_rating}\n\n"
                
                response_text += f"<b>📊 Condition Assessment:</b>\n"
                response_text += f"{analysis.condition_assessment}\n\n"
                
                if analysis.maintenance_recommendations:
                    response_text += f"<b>🔧 Maintenance Recommendations:</b>\n"
                    for i, rec in enumerate(analysis.maintenance_recommendations[:3], 1):
                        response_text += f"{i}. {rec}\n"
                    response_text += "\n"
                
                if analysis.replacement_suggestions:
                    response_text += f"<b>💡 Replacement Suggestions:</b>\n"
                    for i, sug in enumerate(analysis.replacement_suggestions[:2], 1):
                        response_text += f"{i}. {sug}\n"
                    response_text += "\n"
                
                response_text += f"<b>📈 Estimated Value:</b> {analysis.estimated_value}\n"
                response_text += f"<b>🎯 Confidence:</b> {analysis.confidence_score:.1%}\n\n"
                response_text += f"<i>Analysis powered by Gemma3-4b AI</i>"
                
                # Create action buttons
                keyboard = InlineKeyboardBuilder()
                keyboard.add(
                    InlineKeyboardButton(text="🔍 Deep Search", callback_data=f"ai_search_{analysis.model}"),
                    InlineKeyboardButton(text="📋 Generate Quote", callback_data=f"ai_quote_{analysis.equipment_type}"),
                    InlineKeyboardButton(text="📅 Schedule Service", callback_data=f"ai_schedule_{analysis.manufacturer}")
                )
                keyboard.adjust(1, 2)
                
                await analyzing_msg.edit_text(
                    response_text,
                    reply_markup=keyboard.as_markup(),
                    parse_mode="HTML"
                )
                
            else:
                await analyzing_msg.edit_text(
                    "❌ <b>AI Analysis Unavailable</b>\n\n"
                    "AI Engine is not ready. Please try again later.",
                    parse_mode="HTML"
                )
                
        except Exception as e:
            logger.error(f"AI analysis error: {e}")
            await analyzing_msg.edit_text(
                f"❌ <b>AI Analysis Failed</b>\n\n"
                f"Query: {query}\n"
                f"Error: {str(e)}\n\n"
                "Please try again or use /search for basic lookup.",
                parse_mode="HTML"
            )
    
    @router.message(Command("ask"))
    async def ai_chat_handler(message: Message):
        """Handle /ask command for AI-powered chat."""
        question = message.text.replace("/ask", "").strip()
        
        if not question:
            await message.answer(
                "💬 <b>Ask AI Assistant</b>\n\n"
                "Ask me anything about HVAC systems!\n\n"
                "<b>Usage:</b> /ask [your question]\n\n"
                "<b>Examples:</b>\n"
                "• /ask What's the best AC for a 2000 sq ft home?\n"
                "• /ask How often should I change my air filter?\n"
                "• /ask Why is my heat pump not heating properly?",
                parse_mode="HTML"
            )
            return
        
        # Show thinking message
        thinking_msg = await message.answer(
            f"💭 <b>AI Assistant Thinking...</b>\n\n"
            f"Question: {question}\n\n"
            "⏳ Consulting HVAC knowledge base...",
            parse_mode="HTML"
        )
        
        try:
            if bot_manager.ai_engine:
                # Get conversation history from MCP
                conversation_history = []
                if bot_manager.mcp_manager:
                    # This would retrieve actual conversation history
                    conversation_history = [f"Previous question about HVAC systems"]
                
                # Get AI response
                ai_response = await bot_manager.ai_engine.process_customer_query(
                    query=question,
                    context={
                        "user_id": message.from_user.id,
                        "username": message.from_user.username,
                        "chat_type": "telegram",
                        "timestamp": message.date.isoformat()
                    },
                    conversation_history=conversation_history
                )
                
                # Format response
                response_text = f"💬 <b>AI Assistant Response</b>\n\n"
                response_text += f"<b>Question:</b> {question}\n\n"
                response_text += f"<b>Answer:</b>\n{ai_response.content}\n\n"
                
                if ai_response.suggestions:
                    response_text += f"<b>💡 Suggestions:</b>\n"
                    for i, suggestion in enumerate(ai_response.suggestions[:3], 1):
                        response_text += f"{i}. {suggestion}\n"
                    response_text += "\n"
                
                response_text += f"<b>🎯 Confidence:</b> {ai_response.confidence:.1%}\n"
                response_text += f"<b>⚡ Response Time:</b> {ai_response.processing_time:.2f}s\n\n"
                response_text += f"<i>{ai_response.reasoning}</i>"
                
                # Create follow-up buttons
                keyboard = InlineKeyboardBuilder()
                keyboard.add(
                    InlineKeyboardButton(text="🔍 Related Search", callback_data=f"ai_related_{question[:20]}"),
                    InlineKeyboardButton(text="📞 Contact Expert", callback_data="ai_contact_expert"),
                    InlineKeyboardButton(text="💾 Save Answer", callback_data=f"ai_save_{message.message_id}")
                )
                keyboard.adjust(1, 2)
                
                await thinking_msg.edit_text(
                    response_text,
                    reply_markup=keyboard.as_markup(),
                    parse_mode="HTML"
                )
                
                # Save to MCP memory
                if bot_manager.mcp_manager:
                    await bot_manager.mcp_manager.save_project_state({
                        "component": "ai_chat",
                        "user_id": message.from_user.id,
                        "question": question,
                        "answer": ai_response.content,
                        "confidence": ai_response.confidence,
                        "timestamp": message.date.isoformat()
                    })
                
            else:
                await thinking_msg.edit_text(
                    "❌ <b>AI Assistant Unavailable</b>\n\n"
                    "AI Engine is not ready. Please try again later.",
                    parse_mode="HTML"
                )
                
        except Exception as e:
            logger.error(f"AI chat error: {e}")
            await thinking_msg.edit_text(
                f"❌ <b>AI Assistant Error</b>\n\n"
                f"Question: {question}\n"
                f"Error: {str(e)}\n\n"
                "Please try rephrasing your question.",
                parse_mode="HTML"
            )
    
    @router.message(Command("predict"))
    async def ai_predict_handler(message: Message):
        """Handle /predict command for maintenance prediction."""
        equipment_info = message.text.replace("/predict", "").strip()
        
        if not equipment_info:
            await message.answer(
                "🔮 <b>AI Maintenance Prediction</b>\n\n"
                "Provide equipment information for maintenance prediction.\n\n"
                "<b>Usage:</b> /predict [equipment info]\n\n"
                "<b>Example:</b>\n"
                "/predict LG split AC, installed 2020, residential use, last service 6 months ago",
                parse_mode="HTML"
            )
            return
        
        # Show prediction message
        predicting_msg = await message.answer(
            f"🔮 <b>AI Maintenance Prediction...</b>\n\n"
            f"Equipment: {equipment_info}\n\n"
            "⏳ Analyzing usage patterns and predicting maintenance needs...",
            parse_mode="HTML"
        )
        
        try:
            if bot_manager.ai_engine:
                # Prepare equipment information
                equipment_data = {
                    "description": equipment_info,
                    "analysis_type": "maintenance_prediction",
                    "timestamp": message.date.isoformat()
                }
                
                # Get maintenance prediction
                prediction = await bot_manager.ai_engine.predict_maintenance(
                    equipment_info=equipment_data,
                    usage_data={"usage_type": "residential", "frequency": "daily"},
                    environment={"climate": "temperate", "location": "indoor"}
                )
                
                # Format prediction response
                response_text = f"🔮 <b>AI Maintenance Prediction</b>\n\n"
                response_text += f"<b>Equipment:</b> {equipment_info}\n\n"
                response_text += f"<b>🔧 Prediction:</b>\n{prediction['prediction']}\n\n"
                response_text += f"<b>🎯 Confidence:</b> {prediction['confidence']:.1%}\n"
                response_text += f"<b>📅 Generated:</b> {prediction['generated_at'][:19]}\n\n"
                response_text += f"<i>Prediction by {prediction['model_used']}</i>"
                
                # Create action buttons
                keyboard = InlineKeyboardBuilder()
                keyboard.add(
                    InlineKeyboardButton(text="📅 Schedule Maintenance", callback_data="ai_schedule_maintenance"),
                    InlineKeyboardButton(text="💰 Get Quote", callback_data="ai_maintenance_quote"),
                    InlineKeyboardButton(text="📊 Detailed Analysis", callback_data="ai_detailed_analysis")
                )
                keyboard.adjust(1, 2)
                
                await predicting_msg.edit_text(
                    response_text,
                    reply_markup=keyboard.as_markup(),
                    parse_mode="HTML"
                )
                
            else:
                await predicting_msg.edit_text(
                    "❌ <b>AI Prediction Unavailable</b>\n\n"
                    "AI Engine is not ready. Please try again later.",
                    parse_mode="HTML"
                )
                
        except Exception as e:
            logger.error(f"AI prediction error: {e}")
            await predicting_msg.edit_text(
                f"❌ <b>AI Prediction Failed</b>\n\n"
                f"Equipment: {equipment_info}\n"
                f"Error: {str(e)}\n\n"
                "Please try again with more specific information.",
                parse_mode="HTML"
            )
    
    @router.message(Command("quote"))
    async def ai_quote_handler(message: Message):
        """Handle /quote command for AI-generated quotes."""
        requirements = message.text.replace("/quote", "").strip()
        
        if not requirements:
            await message.answer(
                "💰 <b>AI Quote Generator</b>\n\n"
                "Describe your HVAC requirements for an AI-generated quote.\n\n"
                "<b>Usage:</b> /quote [requirements]\n\n"
                "<b>Example:</b>\n"
                "/quote Install central AC for 1500 sq ft home, 2 floors, existing ductwork",
                parse_mode="HTML"
            )
            return
        
        # Show quote generation message
        generating_msg = await message.answer(
            f"💰 <b>AI Quote Generation...</b>\n\n"
            f"Requirements: {requirements}\n\n"
            "⏳ Analyzing requirements and calculating costs...",
            parse_mode="HTML"
        )
        
        try:
            if bot_manager.ai_engine:
                # Prepare requirements
                requirements_data = {
                    "description": requirements,
                    "customer_id": message.from_user.id,
                    "request_date": message.date.isoformat(),
                    "source": "telegram_bot"
                }
                
                # Generate quote
                quote = await bot_manager.ai_engine.generate_quote(
                    requirements=requirements_data,
                    equipment_specs={"type": "standard", "efficiency": "high"},
                    complexity="medium"
                )
                
                # Format quote response
                response_text = f"💰 <b>AI-Generated Quote</b>\n\n"
                response_text += f"<b>Requirements:</b> {requirements}\n\n"
                response_text += f"<b>📋 Quote Details:</b>\n{quote['quote_content']}\n\n"
                response_text += f"<b>📅 Quote ID:</b> {quote['quote_id']}\n"
                response_text += f"<b>⏰ Valid Until:</b> {quote['valid_until']}\n"
                response_text += f"<b>🎯 Confidence:</b> {quote['confidence']:.1%}\n\n"
                response_text += f"<i>⚠️ This is an AI-generated estimate. Final pricing may vary.</i>"
                
                # Create action buttons
                keyboard = InlineKeyboardBuilder()
                keyboard.add(
                    InlineKeyboardButton(text="📞 Contact Sales", callback_data="ai_contact_sales"),
                    InlineKeyboardButton(text="📧 Email Quote", callback_data=f"ai_email_quote_{quote['quote_id']}"),
                    InlineKeyboardButton(text="🔄 Refine Quote", callback_data="ai_refine_quote")
                )
                keyboard.adjust(1, 2)
                
                await generating_msg.edit_text(
                    response_text,
                    reply_markup=keyboard.as_markup(),
                    parse_mode="HTML"
                )
                
            else:
                await generating_msg.edit_text(
                    "❌ <b>AI Quote Generator Unavailable</b>\n\n"
                    "AI Engine is not ready. Please try again later.",
                    parse_mode="HTML"
                )
                
        except Exception as e:
            logger.error(f"AI quote generation error: {e}")
            await generating_msg.edit_text(
                f"❌ <b>AI Quote Generation Failed</b>\n\n"
                f"Requirements: {requirements}\n"
                f"Error: {str(e)}\n\n"
                "Please try again or contact our sales team directly.",
                parse_mode="HTML"
            )
    
    @router.callback_query(lambda c: c.data.startswith("ai_"))
    async def ai_callback_handler(callback_query: CallbackQuery):
        """Handle AI-related callback buttons."""
        action = callback_query.data
        
        if action.startswith("ai_search_"):
            model = action.replace("ai_search_", "")
            await callback_query.message.answer(
                f"🔍 Searching for detailed information about {model}...\n"
                f"Use /search {model} for comprehensive results."
            )
        
        elif action.startswith("ai_quote_"):
            equipment_type = action.replace("ai_quote_", "")
            await callback_query.message.answer(
                f"💰 Generating quote for {equipment_type}...\n"
                f"Use /quote {equipment_type} installation for detailed pricing."
            )
        
        elif action == "ai_contact_expert":
            await callback_query.message.answer(
                "📞 <b>Contact HVAC Expert</b>\n\n"
                "Our experts are available to help!\n\n"
                "📧 Email: <EMAIL>\n"
                "📱 Phone: +1 (555) 123-HVAC\n"
                "💬 Live Chat: Available 24/7",
                parse_mode="HTML"
            )
        
        elif action == "ai_schedule_maintenance":
            await callback_query.message.answer(
                "📅 <b>Schedule Maintenance</b>\n\n"
                "Maintenance scheduling feature coming soon!\n\n"
                "For now, please contact our service team:\n"
                "📞 +1 (555) 123-HVAC"
            )
        
        await callback_query.answer()
    
    logger.info("✅ AI handlers setup completed")
