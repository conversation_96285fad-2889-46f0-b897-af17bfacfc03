# 🚀 ADVANCED HVAC CRM Telegram Bot v2.0

Next-generation AI-powered Telegram bot for HVAC equipment research and CRM management. Features cutting-edge AI capabilities, real-time integrations, advanced analytics, and production-ready architecture.

## 🌟 **FULL POTENTIAL UNLEASHED**

This isn't just a bot - it's a comprehensive AI-powered HVAC assistant that brings together the best of modern technology:

### 🧠 **Advanced AI Engine**
- **LM Studio Integration**: Direct connection to Gemma3-4b model
- **Natural Language Understanding**: Conversational AI for HVAC queries
- **Equipment Analysis**: AI-powered equipment identification and assessment
- **Predictive Maintenance**: ML-based maintenance scheduling and recommendations
- **Quote Generation**: Intelligent pricing and proposal creation
- **Sentiment Analysis**: Customer satisfaction monitoring

### 🕷️ **Krabulon Deep Crawling**
- **Real-time Data**: Live equipment specifications from manufacturer websites
- **Multi-manufacturer Support**: LG, Daikin, Carrier, Trane, and more
- **Image Extraction**: Product photos and technical diagrams
- **Specification Mining**: Detailed technical data and pricing
- **Market Intelligence**: Competitive analysis and trends

### ⚡ **Real-time Integrations**
- **Live Data Feeds**: WebSocket connections for real-time updates
- **System Monitoring**: Continuous health checks and performance tracking
- **Alert Management**: Intelligent notification system
- **Event Streaming**: Real-time user activity and system events
- **Performance Metrics**: Live dashboard with key indicators

### 📊 **Advanced Analytics Engine**
- **User Behavior Tracking**: Comprehensive user interaction analysis
- **Business Intelligence**: Conversion funnels and engagement metrics
- **Performance Analytics**: Response times, error rates, success metrics
- **Predictive Insights**: AI-powered business recommendations
- **Custom Dashboards**: Real-time monitoring and reporting

### 💾 **MCP Memory System**
- **Persistent Context**: Conversation history across sessions
- **Project Tracking**: Development progress and milestones
- **Research Archive**: All searches and results permanently stored
- **State Management**: System configuration and user preferences

### 📄 **Enhanced Document Processing**
- **OCR Technology**: Extract text from images and PDFs
- **Audio Transcription**: Voice message processing with STT
- **Image Analysis**: Equipment identification from photos
- **Data Import**: CSV, Excel, and database integration
- **File Validation**: Security and format checking

## 🚀 Quick Start

### 1. Prerequisites

```bash
# Install dependencies
pip install -r requirements.txt

# Or using UV (recommended)
uv pip install -r requirements.txt
```

### 2. Configuration

Create a `.env` file or set environment variables:

```bash
# Required
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Optional - Webhook mode
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook
TELEGRAM_WEBHOOK_SECRET=your_secret_here
TELEGRAM_USE_WEBHOOK=false

# Server settings
TELEGRAM_BOT_HOST=0.0.0.0
TELEGRAM_BOT_PORT=8443

# MCP Integration
MCP_MEMORY_URL=http://localhost:3001

# Krabulon Integration
KRABULON_ENABLED=true
KRABULON_BASE_PATH=krabulon

# Admin settings
TELEGRAM_ADMIN_IDS=123456789,987654321

# File handling
TELEGRAM_MAX_FILE_SIZE=20971520  # 20MB
TELEGRAM_UPLOAD_DIR=data/telegram_uploads

# Rate limiting
TELEGRAM_RATE_LIMIT_MESSAGES=30
TELEGRAM_RATE_LIMIT_WINDOW=60

# Logging
TELEGRAM_LOG_LEVEL=INFO
TELEGRAM_LOG_FILE=logs/telegram_bot.log
```

### 3. Launch Advanced Bot

```bash
# 🚀 Launch with all advanced features
python launch_advanced_telegram_bot.py launch --production

# 🧠 AI-powered mode with LM Studio
python launch_advanced_telegram_bot.py launch --lm-studio-url http://*************:1234

# 📊 Real-time monitoring dashboard
python launch_advanced_telegram_bot.py monitor

# ⚡ Performance benchmark
python launch_advanced_telegram_bot.py benchmark

# 🎮 Interactive demo
python launch_advanced_telegram_bot.py demo

# Basic launch (legacy)
python launch_telegram_bot.py start
```

## 🎯 **Advanced Bot Commands**

### 🧠 **AI-Powered Commands**
- `/analyze [equipment info]` - AI equipment analysis with Gemma3-4b
- `/ask [question]` - Natural language HVAC consultation
- `/predict [equipment]` - Predictive maintenance recommendations
- `/quote [requirements]` - AI-generated pricing quotes

### 🔍 **Equipment Research**
- `/search [model]` - Deep equipment search with Krabulon
- `/crawl [manufacturer]` - Real-time website crawling
- `/specs [model]` - Detailed technical specifications

### 📊 **System Management**
- `/status` - Real-time system health dashboard
- `/stats` - Advanced analytics and metrics
- `/health` - Comprehensive system diagnostics
- `/monitor` - Live performance monitoring

### 📄 **Enhanced File Processing**
- **Documents**: PDF, DOC, DOCX with OCR and AI analysis
- **Images**: Equipment photos with AI identification
- **Audio**: Voice messages with transcription and analysis
- **Data**: CSV, Excel with intelligent import

### 🔧 **Admin Commands** (Admin Only)
- `/admin` - Advanced admin control panel
- `/logs` - Real-time system logs with filtering
- `/config` - Dynamic system configuration
- `/analytics` - Business intelligence dashboard
- `/alerts` - Alert management system

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Telegram Bot  │────│   Bot Manager    │────│  MCP Manager    │
│                 │    │                  │    │                 │
│ • Commands      │    │ • Initialization │    │ • Memory Client │
│ • Handlers      │    │ • Status Mgmt    │    │ • State Persist │
│ • Middleware    │    │ • Error Handling │    │ • Progress Track│
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ File Processors │    │ Krabulon Agent   │    │ Database Layer  │
│                 │    │                  │    │                 │
│ • Document OCR  │    │ • HVAC Crawler   │    │ • PostgreSQL    │
│ • Image Analysis│    │ • Equipment DB   │    │ • MongoDB       │
│ • Audio STT     │    │ • Manufacturer   │    │ • Redis Cache   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔧 Integration Details

### Krabulon Deep Crawling
- **Enhanced HVAC Crawler**: Specialized for HVAC equipment data
- **Multi-manufacturer Support**: LG, Daikin, Carrier, Trane
- **Real-time Data**: Fresh equipment specifications and pricing
- **Image Extraction**: Product images and technical diagrams

### MCP Memory System
- **Persistent Context**: Conversation history across sessions
- **Project Tracking**: Development progress and milestones
- **Research Archive**: All searches and results saved
- **Performance Metrics**: System usage and optimization data

## 📊 Monitoring & Analytics

### System Status
- Bot uptime and performance
- Message processing statistics
- Error rates and handling
- Integration health checks

### Usage Analytics
- Command usage patterns
- File processing statistics
- Search query analysis
- User engagement metrics

### Performance Metrics
- Response times
- Crawling success rates
- Memory usage optimization
- Database query performance

## 🛡️ Security Features

### Rate Limiting
- Per-user message limits
- Configurable time windows
- Automatic spam protection

### Admin Controls
- Role-based access control
- Admin-only commands
- System configuration protection

### File Security
- File type validation
- Size limit enforcement
- Secure upload handling
- Automatic cleanup

## 🔄 Development Workflow

### Testing
```bash
# Test bot configuration
python launch_telegram_bot.py test

# Test specific components
python -m pytest telegram_bot/tests/

# Integration testing
python telegram_bot/test_integration.py
```

### Deployment
```bash
# Production deployment with webhook
python launch_telegram_bot.py webhook \
  --webhook-url https://yourdomain.com/webhook \
  --webhook-secret your_secret \
  --port 8443

# Docker deployment
docker build -t hvac-telegram-bot .
docker run -d --env-file .env hvac-telegram-bot
```

## 📝 Logging

Comprehensive logging system with:
- Structured log format
- Multiple log levels (DEBUG, INFO, WARNING, ERROR)
- File and console output
- Log rotation and retention
- Performance tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is part of the HVAC CRM system and follows the same licensing terms.

## 🆘 Support

For support and questions:
- Check the logs: `tail -f logs/telegram_bot.log`
- Use the test command: `python launch_telegram_bot.py test`
- Review system status: `/status` command in Telegram
- Contact the development team

---

**🏠 HVAC CRM Telegram Bot** - Bringing AI-powered equipment research to your fingertips! 🚀
