#!/usr/bin/env python3
"""
Simple test runner for transcription system
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# Basic logging
def log(message):
    print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

async def test_docker_services():
    """Test Docker services availability"""
    log("🐳 Testing Docker services...")
    
    try:
        import aiohttp
        
        services = {
            "nemo_stt": "http://localhost:8889/health",
            "transcription_orchestrator": "http://localhost:9000/health",
            "gemma_integration": "http://*************:1234/v1/models"
        }
        
        results = {}
        
        for service_name, url in services.items():
            try:
                async with aiohttp.ClientSession() as session:
                    timeout = aiohttp.ClientTimeout(total=5)
                    async with session.get(url, timeout=timeout) as response:
                        results[service_name] = {
                            "available": response.status in [200, 404],
                            "status_code": response.status,
                            "url": url
                        }
                        log(f"✅ {service_name}: {response.status}")
            except Exception as e:
                results[service_name] = {
                    "available": False,
                    "error": str(e),
                    "url": url
                }
                log(f"❌ {service_name}: {e}")
        
        return results
        
    except ImportError as e:
        log(f"❌ aiohttp not available: {e}")
        return {"error": "aiohttp not available"}

async def test_email_connection():
    """Test email connection"""
    log("📧 Testing email connection...")
    
    try:
        import imaplib
        import ssl
        
        # Email configuration
        email_config = {
            "host": "imap.gmail.com",
            "port": 993,
            "username": "<EMAIL>",
            "password": "Blaeritipol1"
        }
        
        # Create SSL context
        context = ssl.create_default_context()
        
        # Connect to server
        connection = imaplib.IMAP4_SSL(email_config["host"], email_config["port"], ssl_context=context)
        connection.login(email_config["username"], email_config["password"])
        connection.select('INBOX')
        
        # Get folder status
        status, messages = connection.search(None, 'ALL')
        total_messages = len(messages[0].split()) if messages[0] else 0
        
        connection.close()
        connection.logout()
        
        result = {
            "success": True,
            "total_emails": total_messages,
            "account": email_config["username"]
        }
        
        log(f"✅ Email connection successful: {total_messages} emails found")
        return result
        
    except Exception as e:
        log(f"❌ Email connection failed: {e}")
        return {"success": False, "error": str(e)}

async def test_transcription_service():
    """Test transcription service directly"""
    log("🎤 Testing transcription service...")
    
    try:
        import aiohttp
        
        # Test NeMo service health
        async with aiohttp.ClientSession() as session:
            timeout = aiohttp.ClientTimeout(total=10)
            async with session.get("http://localhost:8889/health", timeout=timeout) as response:
                if response.status == 200:
                    log("✅ NeMo transcription service is available")
                    return {"available": True, "status": response.status}
                else:
                    log(f"⚠️ NeMo service returned status: {response.status}")
                    return {"available": False, "status": response.status}
                    
    except Exception as e:
        log(f"❌ Transcription service test failed: {e}")
        return {"available": False, "error": str(e)}

async def run_basic_tests():
    """Run basic system tests"""
    log("🎯 STARTING BASIC TRANSCRIPTION SYSTEM TESTS")
    log("=" * 50)
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {}
    }
    
    # Test 1: Docker services
    log("📋 TEST 1: Docker Services")
    results["tests"]["docker_services"] = await test_docker_services()
    
    # Test 2: Email connection
    log("📋 TEST 2: Email Connection")
    results["tests"]["email_connection"] = await test_email_connection()
    
    # Test 3: Transcription service
    log("📋 TEST 3: Transcription Service")
    results["tests"]["transcription_service"] = await test_transcription_service()
    
    # Save results
    report_file = Path("basic_test_results.json")
    with open(report_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    log("=" * 50)
    log(f"📊 Basic tests completed! Results saved to: {report_file}")
    
    # Print summary
    print("\n🎯 BASIC TEST SUMMARY")
    print("=" * 30)
    
    for test_name, test_result in results["tests"].items():
        if isinstance(test_result, dict):
            if test_result.get("success") or test_result.get("available"):
                print(f"✅ {test_name}: PASS")
            else:
                print(f"❌ {test_name}: FAIL")
        else:
            print(f"⚠️ {test_name}: UNKNOWN")
    
    return results

if __name__ == "__main__":
    asyncio.run(run_basic_tests())
